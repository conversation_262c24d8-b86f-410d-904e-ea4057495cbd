using System;
using System.IO;
using System.Text.Json;
using System.Windows.Forms;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Services
{
    /// <summary>
    /// 配置管理服务
    /// </summary>
    public class ConfigService
    {
        private static readonly Lazy<ConfigService> _instance = new Lazy<ConfigService>(() => new ConfigService());
        public static ConfigService Instance => _instance.Value;

        private readonly string _configDirectory;
        private readonly string _mainConfigFile;
        private AppConfig? _currentConfig;

        private ConfigService()
        {
            _configDirectory = Path.Combine(Application.StartupPath, "Config");
            _mainConfigFile = Path.Combine(_configDirectory, "AppConfig.json");
            
            // 确保配置目录存在
            if (!Directory.Exists(_configDirectory))
            {
                Directory.CreateDirectory(_configDirectory);
            }
        }

        /// <summary>
        /// 获取当前配置
        /// </summary>
        public AppConfig GetConfig()
        {
            if (_currentConfig == null)
            {
                LoadConfig();
            }
            return _currentConfig!;
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        public void LoadConfig()
        {
            try
            {
                if (File.Exists(_mainConfigFile))
                {
                    string json = File.ReadAllText(_mainConfigFile);
                    _currentConfig = JsonSerializer.Deserialize<AppConfig>(json, GetJsonOptions());
                }
                
                // 如果配置为空或加载失败，使用默认配置
                if (_currentConfig == null)
                {
                    _currentConfig = CreateDefaultConfig();
                }
            }
            catch (Exception ex)
            {
                // 配置文件损坏，使用默认配置
                _currentConfig = CreateDefaultConfig();
                LogError($"配置文件加载失败，使用默认配置: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void SaveConfig()
        {
            try
            {
                if (_currentConfig != null)
                {
                    string json = JsonSerializer.Serialize(_currentConfig, GetJsonOptions());
                    File.WriteAllText(_mainConfigFile, json);
                }
            }
            catch (Exception ex)
            {
                LogError($"配置文件保存失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新配置
        /// </summary>
        public void UpdateConfig(AppConfig config)
        {
            _currentConfig = config;
            SaveConfig();
        }

        /// <summary>
        /// 导出配置
        /// </summary>
        public bool ExportConfig(string exportPath)
        {
            try
            {
                // 创建导出目录
                string exportDir = Path.Combine(exportPath, $"PPT批量处理配置_{DateTime.Now:yyyyMMdd_HHmmss}");
                Directory.CreateDirectory(exportDir);

                // 复制所有配置文件
                foreach (string file in Directory.GetFiles(_configDirectory, "*.json"))
                {
                    string fileName = Path.GetFileName(file);
                    string destFile = Path.Combine(exportDir, fileName);
                    File.Copy(file, destFile, true);
                }

                // 创建说明文件
                string readmeFile = Path.Combine(exportDir, "说明.txt");
                File.WriteAllText(readmeFile, $"PPT批量处理工具配置文件\n导出时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n\n使用方法：\n1. 将此文件夹中的所有.json文件复制到软件的Config目录下\n2. 重启软件即可应用配置");

                return true;
            }
            catch (Exception ex)
            {
                LogError($"配置导出失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 导入配置
        /// </summary>
        public bool ImportConfig(string importPath)
        {
            try
            {
                if (Directory.Exists(importPath))
                {
                    // 备份当前配置
                    BackupCurrentConfig();

                    // 导入新配置
                    foreach (string file in Directory.GetFiles(importPath, "*.json"))
                    {
                        string fileName = Path.GetFileName(file);
                        string destFile = Path.Combine(_configDirectory, fileName);
                        File.Copy(file, destFile, true);
                    }

                    // 重新加载配置
                    LoadConfig();
                    return true;
                }
                else if (File.Exists(importPath) && Path.GetExtension(importPath).ToLower() == ".json")
                {
                    // 单个配置文件导入
                    string fileName = Path.GetFileName(importPath);
                    string destFile = Path.Combine(_configDirectory, fileName);
                    File.Copy(importPath, destFile, true);

                    if (fileName == "AppConfig.json")
                    {
                        LoadConfig();
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LogError($"配置导入失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 备份当前配置
        /// </summary>
        private void BackupCurrentConfig()
        {
            try
            {
                string backupDir = Path.Combine(_configDirectory, "Backup");
                if (!Directory.Exists(backupDir))
                {
                    Directory.CreateDirectory(backupDir);
                }

                string backupSubDir = Path.Combine(backupDir, DateTime.Now.ToString("yyyyMMdd_HHmmss"));
                Directory.CreateDirectory(backupSubDir);

                foreach (string file in Directory.GetFiles(_configDirectory, "*.json"))
                {
                    string fileName = Path.GetFileName(file);
                    string destFile = Path.Combine(backupSubDir, fileName);
                    File.Copy(file, destFile, true);
                }
            }
            catch (Exception ex)
            {
                LogError($"配置备份失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private AppConfig CreateDefaultConfig()
        {
            var config = new AppConfig();
            
            // 初始化功能启用状态
            string[] functionNames = {
                "页面设置", "内容删除设置", "内容替换设置",
                "PPT格式设置", "匹配段落格式", "页眉页脚设置",
                "文档属性", "文件名替换", "PPT格式转换"
            };

            foreach (string functionName in functionNames)
            {
                config.FunctionEnabled[functionName] = false;
            }

            return config;
        }

        /// <summary>
        /// 获取JSON序列化选项
        /// </summary>
        private JsonSerializerOptions GetJsonOptions()
        {
            return new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        private void LogError(string message)
        {
            try
            {
                string logFile = Path.Combine(Application.StartupPath, "Log", $"Error_{DateTime.Now:yyyyMMdd}.log");
                string logDir = Path.GetDirectoryName(logFile)!;
                
                if (!Directory.Exists(logDir))
                {
                    Directory.CreateDirectory(logDir);
                }

                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}\n";
                File.AppendAllText(logFile, logEntry);
            }
            catch
            {
                // 忽略日志记录错误
            }
        }
    }
}
