using System;
using System.Linq;
using System.Windows.Forms;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 内容替换设置窗体 - 设置加载和保存
    /// </summary>
    public partial class ContentReplacementForm
    {
        #region 文本替换设置

        /// <summary>
        /// 加载文本替换设置
        /// </summary>
        private void LoadTextReplacementSettings()
        {
            var settings = _currentSettings.TextReplacement;

            // 加载主开关
            var chkMaster = FindControlInPanel<CheckBox>("TextReplacement", "chkTextReplacementMaster");
            if (chkMaster != null)
                chkMaster.Checked = settings.EnableTextReplacement;

            // 加载替换类型选项
            var chkNormal = FindControlInPanel<CheckBox>("TextReplacement", "chkEnableNormalTextReplacement");
            if (chkNormal != null)
                chkNormal.Checked = settings.EnableNormalTextReplacement;

            var chkRegex = FindControlInPanel<CheckBox>("TextReplacement", "chkEnableRegexReplacement");
            if (chkRegex != null)
                chkRegex.Checked = settings.EnableRegexReplacement;

            var chkBatch = FindControlInPanel<CheckBox>("TextReplacement", "chkEnableBatchTextReplacement");
            if (chkBatch != null)
                chkBatch.Checked = settings.EnableBatchTextReplacement;

            var chkRange = FindControlInPanel<CheckBox>("TextReplacement", "chkEnableRangeReplacement");
            if (chkRange != null)
                chkRange.Checked = settings.EnableRangeReplacement;

            // 加载替换范围设置
            var chkTitles = FindControlInPanel<CheckBox>("TextReplacement", "chkReplaceInTitles");
            if (chkTitles != null)
                chkTitles.Checked = settings.ReplacementRange.ReplaceInTitles;

            var chkContent = FindControlInPanel<CheckBox>("TextReplacement", "chkReplaceInContent");
            if (chkContent != null)
                chkContent.Checked = settings.ReplacementRange.ReplaceInContent;

            var chkNotes = FindControlInPanel<CheckBox>("TextReplacement", "chkReplaceInNotes");
            if (chkNotes != null)
                chkNotes.Checked = settings.ReplacementRange.ReplaceInNotes;

            var chkTables = FindControlInPanel<CheckBox>("TextReplacement", "chkReplaceInTables");
            if (chkTables != null)
                chkTables.Checked = settings.ReplacementRange.ReplaceInTables;

            var chkCharts = FindControlInPanel<CheckBox>("TextReplacement", "chkReplaceInCharts");
            if (chkCharts != null)
                chkCharts.Checked = settings.ReplacementRange.ReplaceInCharts;

            // 加载文本替换规则
            LoadTextReplacementRules();
        }

        /// <summary>
        /// 保存文本替换设置
        /// </summary>
        private void SaveTextReplacementSettings()
        {
            var settings = _currentSettings.TextReplacement;

            // 保存主开关
            var chkMaster = FindControlInPanel<CheckBox>("TextReplacement", "chkTextReplacementMaster");
            if (chkMaster != null)
                settings.EnableTextReplacement = chkMaster.Checked;

            // 保存替换类型选项
            var chkNormal = FindControlInPanel<CheckBox>("TextReplacement", "chkEnableNormalTextReplacement");
            if (chkNormal != null)
                settings.EnableNormalTextReplacement = chkNormal.Checked;

            var chkRegex = FindControlInPanel<CheckBox>("TextReplacement", "chkEnableRegexReplacement");
            if (chkRegex != null)
                settings.EnableRegexReplacement = chkRegex.Checked;

            var chkBatch = FindControlInPanel<CheckBox>("TextReplacement", "chkEnableBatchTextReplacement");
            if (chkBatch != null)
                settings.EnableBatchTextReplacement = chkBatch.Checked;

            var chkRange = FindControlInPanel<CheckBox>("TextReplacement", "chkEnableRangeReplacement");
            if (chkRange != null)
                settings.EnableRangeReplacement = chkRange.Checked;

            // 保存替换范围设置
            var chkTitles = FindControlInPanel<CheckBox>("TextReplacement", "chkReplaceInTitles");
            if (chkTitles != null)
                settings.ReplacementRange.ReplaceInTitles = chkTitles.Checked;

            var chkContent = FindControlInPanel<CheckBox>("TextReplacement", "chkReplaceInContent");
            if (chkContent != null)
                settings.ReplacementRange.ReplaceInContent = chkContent.Checked;

            var chkNotes = FindControlInPanel<CheckBox>("TextReplacement", "chkReplaceInNotes");
            if (chkNotes != null)
                settings.ReplacementRange.ReplaceInNotes = chkNotes.Checked;

            var chkTables = FindControlInPanel<CheckBox>("TextReplacement", "chkReplaceInTables");
            if (chkTables != null)
                settings.ReplacementRange.ReplaceInTables = chkTables.Checked;

            var chkCharts = FindControlInPanel<CheckBox>("TextReplacement", "chkReplaceInCharts");
            if (chkCharts != null)
                settings.ReplacementRange.ReplaceInCharts = chkCharts.Checked;

            // 保存文本替换规则
            SaveTextReplacementRules();
        }

        /// <summary>
        /// 加载文本替换规则
        /// </summary>
        private void LoadTextReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("TextReplacement", "listViewTextReplacementRules");
            if (listView == null) return;

            listView.Items.Clear();

            foreach (var rule in _currentSettings.TextReplacement.ReplacementRules)
            {
                var item = new ListViewItem(rule.IsEnabled.ToString())
                {
                    Checked = rule.IsEnabled,
                    Tag = rule
                };

                item.SubItems.Add(rule.RuleName);
                item.SubItems.Add(rule.FindText);
                item.SubItems.Add(rule.ReplaceText);
                item.SubItems.Add(rule.UseRegex ? "是" : "否");
                item.SubItems.Add(rule.CaseSensitive ? "是" : "否");
                item.SubItems.Add(rule.WholeWord ? "是" : "否");

                listView.Items.Add(item);
            }
        }

        /// <summary>
        /// 保存文本替换规则
        /// </summary>
        private void SaveTextReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("TextReplacement", "listViewTextReplacementRules");
            if (listView == null) return;

            _currentSettings.TextReplacement.ReplacementRules.Clear();

            foreach (ListViewItem item in listView.Items)
            {
                if (item.Tag is TextReplacementRule rule)
                {
                    rule.IsEnabled = item.Checked;
                    _currentSettings.TextReplacement.ReplacementRules.Add(rule);
                }
            }
        }

        #endregion

        #region 形状替换设置

        /// <summary>
        /// 加载形状替换设置
        /// </summary>
        private void LoadShapeReplacementSettings()
        {
            var settings = _currentSettings.ShapeReplacement;

            // 加载主开关
            var chkMaster = FindControlInPanel<CheckBox>("ShapeReplacement", "chkShapeReplacementMaster");
            if (chkMaster != null)
                chkMaster.Checked = settings.EnableShapeReplacement;

            // 加载形状替换类型选项
            var chkImage = FindControlInPanel<CheckBox>("ShapeReplacement", "chkEnableImageReplacement");
            if (chkImage != null)
                chkImage.Checked = settings.EnableImageReplacement;

            var chkTextBox = FindControlInPanel<CheckBox>("ShapeReplacement", "chkEnableTextBoxReplacement");
            if (chkTextBox != null)
                chkTextBox.Checked = settings.EnableTextBoxReplacement;

            var chkShapeStyle = FindControlInPanel<CheckBox>("ShapeReplacement", "chkEnableShapeStyleReplacement");
            if (chkShapeStyle != null)
                chkShapeStyle.Checked = settings.EnableShapeStyleReplacement;

            // 加载各种形状替换规则
            LoadImageReplacementRules();
            LoadTextBoxReplacementRules();
            LoadShapeStyleReplacementRules();
        }

        /// <summary>
        /// 保存形状替换设置
        /// </summary>
        private void SaveShapeReplacementSettings()
        {
            var settings = _currentSettings.ShapeReplacement;

            // 保存主开关
            var chkMaster = FindControlInPanel<CheckBox>("ShapeReplacement", "chkShapeReplacementMaster");
            if (chkMaster != null)
                settings.EnableShapeReplacement = chkMaster.Checked;

            // 保存形状替换类型选项
            var chkImage = FindControlInPanel<CheckBox>("ShapeReplacement", "chkEnableImageReplacement");
            if (chkImage != null)
                settings.EnableImageReplacement = chkImage.Checked;

            var chkTextBox = FindControlInPanel<CheckBox>("ShapeReplacement", "chkEnableTextBoxReplacement");
            if (chkTextBox != null)
                settings.EnableTextBoxReplacement = chkTextBox.Checked;

            var chkShapeStyle = FindControlInPanel<CheckBox>("ShapeReplacement", "chkEnableShapeStyleReplacement");
            if (chkShapeStyle != null)
                settings.EnableShapeStyleReplacement = chkShapeStyle.Checked;

            // 保存各种形状替换规则
            SaveImageReplacementRules();
            SaveTextBoxReplacementRules();
            SaveShapeStyleReplacementRules();
        }

        /// <summary>
        /// 加载图片替换规则
        /// </summary>
        private void LoadImageReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("ShapeReplacement", "listViewImageReplacementRules");
            if (listView == null) return;

            listView.Items.Clear();

            foreach (var rule in _currentSettings.ShapeReplacement.ImageReplacementRules)
            {
                var item = new ListViewItem(rule.IsEnabled.ToString())
                {
                    Checked = rule.IsEnabled,
                    Tag = rule
                };

                item.SubItems.Add(rule.RuleName);
                item.SubItems.Add(Path.GetFileName(rule.SourceImagePath));
                item.SubItems.Add(Path.GetFileName(rule.TargetImagePath));

                string matchMethod = "";
                if (rule.MatchByFileName) matchMethod = "按文件名";
                else if (rule.MatchByContent) matchMethod = "按内容";
                else if (rule.MatchBySize) matchMethod = "按尺寸";
                else matchMethod = "未知";

                item.SubItems.Add(matchMethod);

                listView.Items.Add(item);
            }
        }

        /// <summary>
        /// 保存图片替换规则
        /// </summary>
        private void SaveImageReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("ShapeReplacement", "listViewImageReplacementRules");
            if (listView == null) return;

            _currentSettings.ShapeReplacement.ImageReplacementRules.Clear();

            foreach (ListViewItem item in listView.Items)
            {
                if (item.Tag is ImageReplacementRule rule)
                {
                    rule.IsEnabled = item.Checked;
                    _currentSettings.ShapeReplacement.ImageReplacementRules.Add(rule);
                }
            }
        }

        /// <summary>
        /// 加载文本框替换规则
        /// </summary>
        private void LoadTextBoxReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("ShapeReplacement", "listViewTextBoxReplacementRules");
            if (listView == null) return;

            listView.Items.Clear();

            foreach (var rule in _currentSettings.ShapeReplacement.TextBoxReplacementRules)
            {
                var item = new ListViewItem(rule.IsEnabled.ToString())
                {
                    Checked = rule.IsEnabled,
                    Tag = rule
                };

                item.SubItems.Add(rule.RuleName);
                item.SubItems.Add(rule.SourceTextContent);
                item.SubItems.Add(rule.TargetTextContent);

                string matchMethod = "";
                if (rule.MatchByContent) matchMethod += "内容 ";
                if (rule.MatchByPosition) matchMethod += "位置 ";
                item.SubItems.Add(matchMethod.Trim());

                listView.Items.Add(item);
            }
        }

        /// <summary>
        /// 保存文本框替换规则
        /// </summary>
        private void SaveTextBoxReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("ShapeReplacement", "listViewTextBoxReplacementRules");
            if (listView == null) return;

            _currentSettings.ShapeReplacement.TextBoxReplacementRules.Clear();

            foreach (ListViewItem item in listView.Items)
            {
                if (item.Tag is TextBoxReplacementRule rule)
                {
                    rule.IsEnabled = item.Checked;
                    _currentSettings.ShapeReplacement.TextBoxReplacementRules.Add(rule);
                }
            }
        }

        /// <summary>
        /// 加载形状样式替换规则
        /// </summary>
        private void LoadShapeStyleReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("ShapeReplacement", "listViewShapeStyleReplacementRules");
            if (listView == null) return;

            listView.Items.Clear();

            foreach (var rule in _currentSettings.ShapeReplacement.ShapeStyleReplacementRules)
            {
                var item = new ListViewItem(rule.IsEnabled.ToString())
                {
                    Checked = rule.IsEnabled,
                    Tag = rule
                };

                item.SubItems.Add(rule.RuleName);
                item.SubItems.Add(rule.SourceShapeType);
                item.SubItems.Add(rule.TargetShapeType);

                string replaceContent = "";
                if (rule.ReplaceFillColor) replaceContent += "填充 ";
                if (rule.ReplaceBorderColor) replaceContent += "边框 ";
                if (rule.ReplaceBorderWidth) replaceContent += "宽度 ";
                item.SubItems.Add(replaceContent.Trim());

                listView.Items.Add(item);
            }
        }

        /// <summary>
        /// 保存形状样式替换规则
        /// </summary>
        private void SaveShapeStyleReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("ShapeReplacement", "listViewShapeStyleReplacementRules");
            if (listView == null) return;

            _currentSettings.ShapeReplacement.ShapeStyleReplacementRules.Clear();

            foreach (ListViewItem item in listView.Items)
            {
                if (item.Tag is ShapeStyleReplacementRule rule)
                {
                    rule.IsEnabled = item.Checked;
                    _currentSettings.ShapeReplacement.ShapeStyleReplacementRules.Add(rule);
                }
            }
        }

        #endregion

        #region 字体替换设置

        /// <summary>
        /// 加载字体替换设置
        /// </summary>
        private void LoadFontReplacementSettings()
        {
            var settings = _currentSettings.FontReplacement;

            // 加载主开关
            var chkMaster = FindControlInPanel<CheckBox>("FontReplacement", "chkFontReplacementMaster");
            if (chkMaster != null)
                chkMaster.Checked = settings.EnableFontReplacement;

            // 加载字体替换类型选项
            var chkFontName = FindControlInPanel<CheckBox>("FontReplacement", "chkEnableFontNameReplacement");
            if (chkFontName != null)
                chkFontName.Checked = settings.EnableFontNameReplacement;

            var chkFontStyle = FindControlInPanel<CheckBox>("FontReplacement", "chkEnableFontStyleReplacement");
            if (chkFontStyle != null)
                chkFontStyle.Checked = settings.EnableFontStyleReplacement;

            var chkFontEmbedding = FindControlInPanel<CheckBox>("FontReplacement", "chkEnableFontEmbedding");
            if (chkFontEmbedding != null)
                chkFontEmbedding.Checked = settings.EnableFontEmbedding;

            // 加载字体嵌入设置
            LoadFontEmbeddingSettings();

            // 加载字体替换规则
            LoadFontNameReplacementRules();
            LoadFontStyleReplacementRules();
        }

        /// <summary>
        /// 保存字体替换设置
        /// </summary>
        private void SaveFontReplacementSettings()
        {
            var settings = _currentSettings.FontReplacement;

            // 保存主开关
            var chkMaster = FindControlInPanel<CheckBox>("FontReplacement", "chkFontReplacementMaster");
            if (chkMaster != null)
                settings.EnableFontReplacement = chkMaster.Checked;

            // 保存字体替换类型选项
            var chkFontName = FindControlInPanel<CheckBox>("FontReplacement", "chkEnableFontNameReplacement");
            if (chkFontName != null)
                settings.EnableFontNameReplacement = chkFontName.Checked;

            var chkFontStyle = FindControlInPanel<CheckBox>("FontReplacement", "chkEnableFontStyleReplacement");
            if (chkFontStyle != null)
                settings.EnableFontStyleReplacement = chkFontStyle.Checked;

            var chkFontEmbedding = FindControlInPanel<CheckBox>("FontReplacement", "chkEnableFontEmbedding");
            if (chkFontEmbedding != null)
                settings.EnableFontEmbedding = chkFontEmbedding.Checked;

            // 保存字体嵌入设置
            SaveFontEmbeddingSettings();

            // 保存字体替换规则
            SaveFontNameReplacementRules();
            SaveFontStyleReplacementRules();
        }

        /// <summary>
        /// 加载字体嵌入设置
        /// </summary>
        private void LoadFontEmbeddingSettings()
        {
            var settings = _currentSettings.FontReplacement.FontEmbeddingSettings;

            var chkEmbedAll = FindControlInPanel<CheckBox>("FontReplacement", "chkEmbedAllFonts");
            if (chkEmbedAll != null)
                chkEmbedAll.Checked = settings.EmbedAllFonts;

            var chkEmbedUsedOnly = FindControlInPanel<CheckBox>("FontReplacement", "chkEmbedUsedCharactersOnly");
            if (chkEmbedUsedOnly != null)
                chkEmbedUsedOnly.Checked = settings.EmbedCharacters == FontEmbedCharacters.OnlyUsed;

            var listBoxFonts = FindControlInPanel<ListBox>("FontReplacement", "listBoxFontsToEmbed");
            if (listBoxFonts != null)
            {
                listBoxFonts.Items.Clear();
                foreach (var font in settings.FontsToEmbed)
                {
                    listBoxFonts.Items.Add(font);
                }
            }
        }

        /// <summary>
        /// 保存字体嵌入设置
        /// </summary>
        private void SaveFontEmbeddingSettings()
        {
            var settings = _currentSettings.FontReplacement.FontEmbeddingSettings;

            var chkEmbedAll = FindControlInPanel<CheckBox>("FontReplacement", "chkEmbedAllFonts");
            if (chkEmbedAll != null)
                settings.EmbedAllFonts = chkEmbedAll.Checked;

            var chkEmbedUsedOnly = FindControlInPanel<CheckBox>("FontReplacement", "chkEmbedUsedCharactersOnly");
            if (chkEmbedUsedOnly != null)
                settings.EmbedCharacters = chkEmbedUsedOnly.Checked ? FontEmbedCharacters.OnlyUsed : FontEmbedCharacters.All;

            var listBoxFonts = FindControlInPanel<ListBox>("FontReplacement", "listBoxFontsToEmbed");
            if (listBoxFonts != null)
            {
                settings.FontsToEmbed.Clear();
                foreach (string font in listBoxFonts.Items)
                {
                    settings.FontsToEmbed.Add(font);
                }
            }
        }

        /// <summary>
        /// 加载字体名称替换规则
        /// </summary>
        private void LoadFontNameReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("FontReplacement", "listViewFontNameReplacementRules");
            if (listView == null) return;

            listView.Items.Clear();

            foreach (var rule in _currentSettings.FontReplacement.FontNameReplacementRules)
            {
                var item = new ListViewItem(rule.IsEnabled.ToString())
                {
                    Checked = rule.IsEnabled,
                    Tag = rule
                };

                item.SubItems.Add(rule.RuleName);
                item.SubItems.Add(rule.SourceFontName);
                item.SubItems.Add(rule.TargetFontName);
                item.SubItems.Add(rule.ExactMatch ? "是" : "否");
                item.SubItems.Add(rule.IncludeSubFonts ? "是" : "否");

                listView.Items.Add(item);
            }
        }

        /// <summary>
        /// 保存字体名称替换规则
        /// </summary>
        private void SaveFontNameReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("FontReplacement", "listViewFontNameReplacementRules");
            if (listView == null) return;

            _currentSettings.FontReplacement.FontNameReplacementRules.Clear();

            foreach (ListViewItem item in listView.Items)
            {
                if (item.Tag is FontNameReplacementRule rule)
                {
                    rule.IsEnabled = item.Checked;
                    _currentSettings.FontReplacement.FontNameReplacementRules.Add(rule);
                }
            }
        }

        /// <summary>
        /// 加载字体样式替换规则
        /// </summary>
        private void LoadFontStyleReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("FontReplacement", "listViewFontStyleReplacementRules");
            if (listView == null) return;

            listView.Items.Clear();

            foreach (var rule in _currentSettings.FontReplacement.FontStyleReplacementRules)
            {
                var item = new ListViewItem(rule.IsEnabled.ToString())
                {
                    Checked = rule.IsEnabled,
                    Tag = rule
                };

                item.SubItems.Add(rule.RuleName);
                item.SubItems.Add(rule.SourceFontName);
                item.SubItems.Add(rule.TargetFontName);

                string replaceContent = "";
                if (rule.ReplaceFontSize) replaceContent += "大小 ";
                if (rule.ReplaceBoldStyle) replaceContent += "粗体 ";
                if (rule.ReplaceItalicStyle) replaceContent += "斜体 ";
                if (rule.ReplaceUnderlineStyle) replaceContent += "下划线 ";
                item.SubItems.Add(replaceContent.Trim());

                listView.Items.Add(item);
            }
        }

        /// <summary>
        /// 保存字体样式替换规则
        /// </summary>
        private void SaveFontStyleReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("FontReplacement", "listViewFontStyleReplacementRules");
            if (listView == null) return;

            _currentSettings.FontReplacement.FontStyleReplacementRules.Clear();

            foreach (ListViewItem item in listView.Items)
            {
                if (item.Tag is FontStyleReplacementRule rule)
                {
                    rule.IsEnabled = item.Checked;
                    _currentSettings.FontReplacement.FontStyleReplacementRules.Add(rule);
                }
            }
        }

        #endregion

        #region 颜色替换设置

        /// <summary>
        /// 加载颜色替换设置
        /// </summary>
        private void LoadColorReplacementSettings()
        {
            var settings = _currentSettings.ColorReplacement;

            // 加载主开关
            var chkMaster = FindControlInPanel<CheckBox>("ColorReplacement", "chkColorReplacementMaster");
            if (chkMaster != null)
                chkMaster.Checked = settings.EnableColorReplacement;

            // 加载颜色替换类型选项
            var chkTheme = FindControlInPanel<CheckBox>("ColorReplacement", "chkEnableThemeColorReplacement");
            if (chkTheme != null)
                chkTheme.Checked = settings.EnableThemeColorReplacement;

            var chkCustom = FindControlInPanel<CheckBox>("ColorReplacement", "chkEnableCustomColorReplacement");
            if (chkCustom != null)
                chkCustom.Checked = settings.EnableCustomColorReplacement;

            // 加载颜色替换规则
            LoadThemeColorReplacementRules();
            LoadCustomColorReplacementRules();
        }

        /// <summary>
        /// 保存颜色替换设置
        /// </summary>
        private void SaveColorReplacementSettings()
        {
            var settings = _currentSettings.ColorReplacement;

            // 保存主开关
            var chkMaster = FindControlInPanel<CheckBox>("ColorReplacement", "chkColorReplacementMaster");
            if (chkMaster != null)
                settings.EnableColorReplacement = chkMaster.Checked;

            // 保存颜色替换类型选项
            var chkTheme = FindControlInPanel<CheckBox>("ColorReplacement", "chkEnableThemeColorReplacement");
            if (chkTheme != null)
                settings.EnableThemeColorReplacement = chkTheme.Checked;

            var chkCustom = FindControlInPanel<CheckBox>("ColorReplacement", "chkEnableCustomColorReplacement");
            if (chkCustom != null)
                settings.EnableCustomColorReplacement = chkCustom.Checked;

            // 保存颜色替换规则
            SaveThemeColorReplacementRules();
            SaveCustomColorReplacementRules();
        }

        /// <summary>
        /// 加载主题颜色替换规则
        /// </summary>
        private void LoadThemeColorReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("ColorReplacement", "listViewThemeColorReplacementRules");
            if (listView == null) return;

            listView.Items.Clear();

            foreach (var rule in _currentSettings.ColorReplacement.ThemeColorReplacementRules)
            {
                var item = new ListViewItem(rule.IsEnabled.ToString())
                {
                    Checked = rule.IsEnabled,
                    Tag = rule
                };

                item.SubItems.Add(rule.RuleName);
                item.SubItems.Add(rule.SourceSchemeColor.ToString());
                item.SubItems.Add(rule.TargetSchemeColor.ToString());

                string applyRange = "";
                if (rule.ApplyToTextColor) applyRange += "文本 ";
                if (rule.ApplyToFillColor) applyRange += "填充 ";
                if (rule.ApplyToBorderColor) applyRange += "边框 ";
                item.SubItems.Add(applyRange.Trim());

                listView.Items.Add(item);
            }
        }

        /// <summary>
        /// 保存主题颜色替换规则
        /// </summary>
        private void SaveThemeColorReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("ColorReplacement", "listViewThemeColorReplacementRules");
            if (listView == null) return;

            _currentSettings.ColorReplacement.ThemeColorReplacementRules.Clear();

            foreach (ListViewItem item in listView.Items)
            {
                if (item.Tag is ThemeColorReplacementRule rule)
                {
                    rule.IsEnabled = item.Checked;
                    _currentSettings.ColorReplacement.ThemeColorReplacementRules.Add(rule);
                }
            }
        }

        /// <summary>
        /// 加载自定义颜色替换规则
        /// </summary>
        private void LoadCustomColorReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("ColorReplacement", "listViewCustomColorReplacementRules");
            if (listView == null) return;

            listView.Items.Clear();

            foreach (var rule in _currentSettings.ColorReplacement.CustomColorReplacementRules)
            {
                var item = new ListViewItem(rule.IsEnabled.ToString())
                {
                    Checked = rule.IsEnabled,
                    Tag = rule
                };

                item.SubItems.Add(rule.RuleName);
                item.SubItems.Add(rule.SourceColor);
                item.SubItems.Add(rule.TargetColor);

                string applyRange = "";
                if (rule.ApplyToTextColor) applyRange += "文本 ";
                if (rule.ApplyToFillColor) applyRange += "填充 ";
                if (rule.ApplyToBorderColor) applyRange += "边框 ";
                if (rule.ApplyToBackgroundColor) applyRange += "背景 ";
                item.SubItems.Add(applyRange.Trim());

                listView.Items.Add(item);
            }
        }

        /// <summary>
        /// 保存自定义颜色替换规则
        /// </summary>
        private void SaveCustomColorReplacementRules()
        {
            var listView = FindControlInPanel<ListView>("ColorReplacement", "listViewCustomColorReplacementRules");
            if (listView == null) return;

            _currentSettings.ColorReplacement.CustomColorReplacementRules.Clear();

            foreach (ListViewItem item in listView.Items)
            {
                if (item.Tag is CustomColorReplacementRule rule)
                {
                    rule.IsEnabled = item.Checked;
                    _currentSettings.ColorReplacement.CustomColorReplacementRules.Add(rule);
                }
            }
        }

        #endregion
    }
}
