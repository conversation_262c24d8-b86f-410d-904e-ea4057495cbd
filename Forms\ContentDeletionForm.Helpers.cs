using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 内容删除设置窗体 - 辅助方法
    /// </summary>
    public partial class ContentDeletionForm
    {
        /// <summary>
        /// 创建可滚动面板
        /// </summary>
        private Panel CreateScrollablePanel()
        {
            return new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(5)
            };
        }

        /// <summary>
        /// 创建复选框
        /// </summary>
        private CheckBox CreateCheckBox(string text, int x, int y)
        {
            return new CheckBox
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(180, 25),
                Font = new Font("Microsoft YaHei UI", 9F),
                UseVisualStyleBackColor = true,
                TextAlign = ContentAlignment.MiddleLeft
            };
        }

        /// <summary>
        /// 创建标签
        /// </summary>
        private Label CreateLabel(string text, int x, int y)
        {
            return new Label
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(80, 25),
                Font = new Font("Microsoft YaHei UI", 9F),
                TextAlign = ContentAlignment.MiddleLeft
            };
        }

        /// <summary>
        /// 创建数值输入框
        /// </summary>
        private NumericUpDown CreateNumericUpDown(decimal min, decimal max, int x, int y)
        {
            var numericUpDown = new NumericUpDown
            {
                Minimum = min,
                Maximum = max,
                Location = new Point(x, y),
                Size = new Size(80, 25),
                Font = new Font("Microsoft YaHei UI", 9F),
                TextAlign = HorizontalAlignment.Center
            };
            return numericUpDown;
        }

        /// <summary>
        /// 创建下拉框
        /// </summary>
        private ComboBox CreateComboBox(string[] items, int x, int y)
        {
            var comboBox = new ComboBox
            {
                Location = new Point(x, y),
                Size = new Size(80, 25),
                Font = new Font("Microsoft YaHei UI", 9F),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            comboBox.Items.AddRange(items);
            if (items.Length > 0)
                comboBox.SelectedIndex = 0;
            return comboBox;
        }

        /// <summary>
        /// 创建文本框
        /// </summary>
        private TextBox CreateTextBox(int x, int y, int width, int height, bool multiline = false)
        {
            return new TextBox
            {
                Location = new Point(x, y),
                Size = new Size(width, height),
                Font = new Font("Microsoft YaHei UI", 9F),
                Multiline = multiline,
                ScrollBars = multiline ? ScrollBars.Vertical : ScrollBars.None,
                TextAlign = HorizontalAlignment.Left
            };
        }

        /// <summary>
        /// 创建按钮
        /// </summary>
        private Button CreateButton(string text, int x, int y, int width, int height)
        {
            return new Button
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, height),
                Font = new Font("Microsoft YaHei UI", 9F),
                UseVisualStyleBackColor = true
            };
        }

        /// <summary>
        /// 创建分组框
        /// </summary>
        private GroupBox CreateGroupBox(string text, int x, int y, int width, int height)
        {
            return new GroupBox
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(width, height),
                Font = new Font("Microsoft YaHei UI", 9F),
                Padding = new Padding(10, 20, 10, 10)
            };
        }

        /// <summary>
        /// 设置复选框值
        /// </summary>
        private void SetCheckBoxValue(Panel panel, string name, bool value)
        {
            var control = FindControlByName(panel, name) as CheckBox;
            if (control != null)
                control.Checked = value;
        }

        /// <summary>
        /// 设置数值输入框值
        /// </summary>
        private void SetNumericUpDownValue(Panel panel, string name, decimal value)
        {
            var control = FindControlByName(panel, name) as NumericUpDown;
            if (control != null)
            {
                if (value >= control.Minimum && value <= control.Maximum)
                    control.Value = value;
            }
        }

        /// <summary>
        /// 设置下拉框值
        /// </summary>
        private void SetComboBoxValue(Panel panel, string name, string value)
        {
            var control = FindControlByName(panel, name) as ComboBox;
            if (control != null)
            {
                var index = control.Items.IndexOf(value);
                if (index >= 0)
                    control.SelectedIndex = index;
            }
        }

        /// <summary>
        /// 设置文本框值
        /// </summary>
        private void SetTextBoxValue(Panel panel, string name, string value)
        {
            var control = FindControlByName(panel, name) as TextBox;
            if (control != null)
                control.Text = value ?? string.Empty;
        }

        /// <summary>
        /// 获取复选框值
        /// </summary>
        private bool GetCheckBoxValue(Panel panel, string name)
        {
            var control = FindControlByName(panel, name) as CheckBox;
            return control?.Checked ?? false;
        }

        /// <summary>
        /// 获取数值输入框值
        /// </summary>
        private decimal GetNumericUpDownValue(Panel panel, string name)
        {
            var control = FindControlByName(panel, name) as NumericUpDown;
            return control?.Value ?? 0;
        }

        /// <summary>
        /// 获取下拉框值
        /// </summary>
        private string GetComboBoxValue(Panel panel, string name)
        {
            var control = FindControlByName(panel, name) as ComboBox;
            return control?.SelectedItem?.ToString() ?? string.Empty;
        }

        /// <summary>
        /// 获取文本框值
        /// </summary>
        private string GetTextBoxValue(Panel panel, string name)
        {
            var control = FindControlByName(panel, name) as TextBox;
            return control?.Text ?? string.Empty;
        }

        /// <summary>
        /// 根据名称查找控件
        /// </summary>
        private Control? FindControlByName(Control parent, string name)
        {
            if (parent.Name == name)
                return parent;

            foreach (Control child in parent.Controls)
            {
                var found = FindControlByName(child, name);
                if (found != null)
                    return found;
            }

            return null;
        }

        /// <summary>
        /// 获取当前设置
        /// </summary>
        public Models.ContentDeletionSettings GetCurrentSettings()
        {
            try
            {
                SaveUIToSettings();
                return _currentSettings;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"获取当前设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new Models.ContentDeletionSettings();
            }
        }

        /// <summary>
        /// 保存界面设置到配置
        /// </summary>
        private void SaveUIToSettings()
        {
            try
            {
                // 保存删除文档设置
                SaveDocumentDeletionSettings();

                // 保存内容删除设置
                SaveContentRemovalSettings();

                // 保存文本删除设置
                SaveTextDeletionSettings();

                // 保存图片删除设置
                SaveImageDeletionSettings();

                // 保存表格删除设置
                SaveTableDeletionSettings();

                // 保存图表删除设置
                SaveChartDeletionSettings();

                // 保存音频视频删除设置
                SaveMediaDeletionSettings();

                // 保存联系方式删除设置
                SaveContactDeletionSettings();

                // 保存动画删除设置
                SaveAnimationDeletionSettings();

                // 保存备注删除设置
                SaveNotesDeletionSettings();

                // 保存格式删除设置
                SaveFormatDeletionSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存界面设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存删除文档设置
        /// </summary>
        private void SaveDocumentDeletionSettings()
        {
            var settings = _currentSettings.DocumentDeletion;
            var panel = _tabPanels["DocumentDeletion"];

            // 文件名长度检查
            settings.EnableFileNameLengthCheck = GetCheckBoxValue(panel, "chkFileNameLengthCheck");
            settings.FileNameMinLength = (int)GetNumericUpDownValue(panel, "numFileNameMinLength");
            settings.FileNameMaxLength = (int)GetNumericUpDownValue(panel, "numFileNameMaxLength");

            // 文件大小检查
            settings.EnableFileSizeCheck = GetCheckBoxValue(panel, "chkFileSizeCheck");
            settings.FileMinSize = (long)GetNumericUpDownValue(panel, "numFileSizeMin");
            settings.FileMaxSize = (long)GetNumericUpDownValue(panel, "numFileSizeMax");
            settings.FileSizeUnit = GetComboBoxValue(panel, "cmbFileSizeUnit");

            // 内容字符数检查
            settings.EnableContentCharCountCheck = GetCheckBoxValue(panel, "chkContentCharCountCheck");
            settings.ContentMinCharCount = (int)GetNumericUpDownValue(panel, "numContentCharMin");
            settings.ContentMaxCharCount = (int)GetNumericUpDownValue(panel, "numContentCharMax");

            // 页数检查
            settings.EnablePageCountCheck = GetCheckBoxValue(panel, "chkPageCountCheck");
            settings.MinPageCount = (int)GetNumericUpDownValue(panel, "numPageMin");
            settings.MaxPageCount = (int)GetNumericUpDownValue(panel, "numPageMax");

            // 非法词检查
            settings.EnableFileNameIllegalWordsCheck = GetCheckBoxValue(panel, "chkFileNameIllegalCheck");
            // 文件名非法词已通过弹窗直接保存到settings中，无需从界面获取

            settings.EnableContentIllegalWordsCheck = GetCheckBoxValue(panel, "chkContentIllegalCheck");
            // 内容非法词已通过弹窗直接保存到settings中，无需从界面获取
        }

        /// <summary>
        /// 保存内容删除设置
        /// </summary>
        private void SaveContentRemovalSettings()
        {
            var settings = _currentSettings.ContentRemoval;
            var panel = _tabPanels["ContentRemoval"];

            settings.DeleteBlankSlides = GetCheckBoxValue(panel, "chkDeleteBlankSlides");
            settings.DeleteFirstSlide = GetCheckBoxValue(panel, "chkDeleteFirstSlide");
            settings.DeleteLastSlide = GetCheckBoxValue(panel, "chkDeleteLastSlide");
            settings.EnableSlideRangeDeletion = GetCheckBoxValue(panel, "chkDeleteSlideRange");
            settings.SlideRangeStart = (int)GetNumericUpDownValue(panel, "numSlideRangeStart");
            settings.SlideRangeEnd = (int)GetNumericUpDownValue(panel, "numSlideRangeEnd");
            settings.EnableKeywordSlidesDeletion = GetCheckBoxValue(panel, "chkDeleteKeywordSlides");
            var slideKeywordsText = GetTextBoxValue(panel, "txtSlideKeywords");
            settings.SlideKeywords = slideKeywordsText.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(k => k.Trim()).ToList();
            settings.DeleteBlankParagraphs = GetCheckBoxValue(panel, "chkDeleteBlankParagraphs");
            settings.DeleteBlankLines = GetCheckBoxValue(panel, "chkDeleteBlankLines");
        }

        /// <summary>
        /// 保存文本删除设置
        /// </summary>
        private void SaveTextDeletionSettings()
        {
            var settings = _currentSettings.TextDeletion;
            var panel = _tabPanels["TextDeletion"];

            settings.DeleteParagraphsWithText = GetCheckBoxValue(panel, "chkDeleteParagraphsWithText");
            var paragraphKeywordsText = GetTextBoxValue(panel, "txtParagraphKeywords");
            settings.ParagraphKeywords = paragraphKeywordsText.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries).ToList();

            settings.DeleteTextBoxesWithText = GetCheckBoxValue(panel, "chkDeleteTextBoxesWithText");
            var textBoxKeywordsText = GetTextBoxValue(panel, "txtTextBoxKeywords");
            settings.TextBoxKeywords = textBoxKeywordsText.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries).ToList();

            settings.DeleteTablesWithText = GetCheckBoxValue(panel, "chkDeleteTablesWithText");
            var tableKeywordsText = GetTextBoxValue(panel, "txtTableKeywords");
            settings.TableKeywords = tableKeywordsText.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries).ToList();
        }

        /// <summary>
        /// 保存图片删除设置
        /// </summary>
        private void SaveImageDeletionSettings()
        {
            var settings = _currentSettings.ImageDeletion;
            var panel = _tabPanels["ImageDeletion"];

            settings.DeleteAllImages = GetCheckBoxValue(panel, "chkDeleteAllImages");
            settings.EnableSpecificImageDeletion = GetCheckBoxValue(panel, "chkDeleteSpecificImages");
            var specificImagesText = GetTextBoxValue(panel, "txtSpecificImageNames");
            settings.SpecificImageNames = specificImagesText.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(k => k.Trim()).ToList();
            settings.DeleteLastSlideImages = GetCheckBoxValue(panel, "chkDeleteLastSlideImages");
            settings.DeleteBackgroundImages = GetCheckBoxValue(panel, "chkDeleteBackgroundImages");
            settings.EnableSlideRangeImageDeletion = GetCheckBoxValue(panel, "chkDeleteImageRange");
            settings.ImageSlideRangeStart = (int)GetNumericUpDownValue(panel, "numImageRangeStart");
            settings.ImageSlideRangeEnd = (int)GetNumericUpDownValue(panel, "numImageRangeEnd");
            settings.EnablePositionImageDeletion = GetCheckBoxValue(panel, "chkDeletePositionImages");
            settings.PositionX = (float)GetNumericUpDownValue(panel, "numPositionX");
            settings.PositionY = (float)GetNumericUpDownValue(panel, "numPositionY");
            settings.PositionWidth = (float)GetNumericUpDownValue(panel, "numPositionWidth");
            settings.PositionHeight = (float)GetNumericUpDownValue(panel, "numPositionHeight");
        }

        /// <summary>
        /// 保存表格删除设置
        /// </summary>
        private void SaveTableDeletionSettings()
        {
            var settings = _currentSettings.TableDeletion;
            var panel = _tabPanels["TableDeletion"];

            settings.DeleteAllTables = GetCheckBoxValue(panel, "chkDeleteAllTables");
            settings.DeleteLastSlideTables = GetCheckBoxValue(panel, "chkDeleteLastSlideTables");
            settings.EnableSlideRangeTableDeletion = GetCheckBoxValue(panel, "chkDeleteTableRange");
            settings.TableSlideRangeStart = (int)GetNumericUpDownValue(panel, "numTableRangeStart");
            settings.TableSlideRangeEnd = (int)GetNumericUpDownValue(panel, "numTableRangeEnd");
        }

        /// <summary>
        /// 保存图表删除设置
        /// </summary>
        private void SaveChartDeletionSettings()
        {
            var settings = _currentSettings.ChartDeletion;
            var panel = _tabPanels["ChartDeletion"];

            settings.DeleteAllCharts = GetCheckBoxValue(panel, "chkDeleteAllCharts");
            settings.DeleteLastSlideCharts = GetCheckBoxValue(panel, "chkDeleteLastSlideCharts");
            settings.EnableSlideRangeChartDeletion = GetCheckBoxValue(panel, "chkDeleteChartRange");
            settings.ChartSlideRangeStart = (int)GetNumericUpDownValue(panel, "numChartRangeStart");
            settings.ChartSlideRangeEnd = (int)GetNumericUpDownValue(panel, "numChartRangeEnd");
        }

        /// <summary>
        /// 保存音频视频删除设置
        /// </summary>
        private void SaveMediaDeletionSettings()
        {
            var settings = _currentSettings.MediaDeletion;
            var panel = _tabPanels["MediaDeletion"];

            settings.DeleteAllAudio = GetCheckBoxValue(panel, "chkDeleteAllAudio");
            settings.DeleteLastSlideAudio = GetCheckBoxValue(panel, "chkDeleteLastSlideAudio");
            settings.EnableSlideRangeAudioDeletion = GetCheckBoxValue(panel, "chkDeleteAudioRange");
            settings.AudioSlideRangeStart = (int)GetNumericUpDownValue(panel, "numAudioRangeStart");
            settings.AudioSlideRangeEnd = (int)GetNumericUpDownValue(panel, "numAudioRangeEnd");
            settings.DeleteAllVideo = GetCheckBoxValue(panel, "chkDeleteAllVideo");
            settings.DeleteLastSlideVideo = GetCheckBoxValue(panel, "chkDeleteLastSlideVideo");
            settings.EnableSlideRangeVideoDeletion = GetCheckBoxValue(panel, "chkDeleteVideoRange");
            settings.VideoSlideRangeStart = (int)GetNumericUpDownValue(panel, "numVideoRangeStart");
            settings.VideoSlideRangeEnd = (int)GetNumericUpDownValue(panel, "numVideoRangeEnd");
        }

        /// <summary>
        /// 保存联系方式删除设置
        /// </summary>
        private void SaveContactDeletionSettings()
        {
            var settings = _currentSettings.ContactDeletion;
            var panel = _tabPanels["ContactDeletion"];

            settings.DeletePhoneNumbers = GetCheckBoxValue(panel, "chkDeletePhoneNumbers");
            settings.DeleteLandlineNumbers = GetCheckBoxValue(panel, "chkDeleteLandlineNumbers");
            settings.DeleteEmailAddresses = GetCheckBoxValue(panel, "chkDeleteEmailAddresses");
            settings.DeleteWebsites = GetCheckBoxValue(panel, "chkDeleteWebsites");
            settings.DeleteHyperlinks = GetCheckBoxValue(panel, "chkDeleteHyperlinks");
        }

        /// <summary>
        /// 保存动画删除设置
        /// </summary>
        private void SaveAnimationDeletionSettings()
        {
            var settings = _currentSettings.AnimationDeletion;
            var panel = _tabPanels["AnimationDeletion"];

            settings.DeleteAllAnimations = GetCheckBoxValue(panel, "chkDeleteAllAnimations");
            settings.DeleteLastSlideAnimations = GetCheckBoxValue(panel, "chkDeleteLastSlideAnimations");
            settings.EnableSlideRangeAnimationDeletion = GetCheckBoxValue(panel, "chkDeleteAnimationRange");
            settings.AnimationSlideRangeStart = (int)GetNumericUpDownValue(panel, "numAnimationRangeStart");
            settings.AnimationSlideRangeEnd = (int)GetNumericUpDownValue(panel, "numAnimationRangeEnd");
            settings.DeleteAllTransitions = GetCheckBoxValue(panel, "chkDeleteAllTransitions");
            settings.DeleteLastSlideTransitions = GetCheckBoxValue(panel, "chkDeleteLastSlideTransitions");
            settings.EnableSlideRangeTransitionDeletion = GetCheckBoxValue(panel, "chkDeleteTransitionRange");
            settings.TransitionSlideRangeStart = (int)GetNumericUpDownValue(panel, "numTransitionRangeStart");
            settings.TransitionSlideRangeEnd = (int)GetNumericUpDownValue(panel, "numTransitionRangeEnd");
        }

        /// <summary>
        /// 保存备注删除设置
        /// </summary>
        private void SaveNotesDeletionSettings()
        {
            var settings = _currentSettings.NotesDeletion;
            var panel = _tabPanels["NotesDeletion"];

            settings.DeleteSlideNotes = GetCheckBoxValue(panel, "chkDeleteSlideNotes");
            settings.ClearNotesContent = GetCheckBoxValue(panel, "chkClearNotesContent");
        }

        /// <summary>
        /// 保存格式删除设置
        /// </summary>
        private void SaveFormatDeletionSettings()
        {
            var settings = _currentSettings.FormatDeletion;
            var panel = _tabPanels["FormatDeletion"];

            settings.DeleteFontFormatting = GetCheckBoxValue(panel, "chkDeleteFontFormatting");
            settings.DeleteParagraphFormatting = GetCheckBoxValue(panel, "chkDeleteParagraphFormatting");
            settings.DeleteTableFormatting = GetCheckBoxValue(panel, "chkDeleteTableFormatting");
            settings.DeleteListFormatting = GetCheckBoxValue(panel, "chkDeleteListFormatting");
        }
    }
}
