using System;
using System.Drawing;
using System.Windows.Forms;
using Aspose.Slides;
using Aspose.Slides.Theme;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// PPT全局格式设置窗体
    /// </summary>
    public partial class PPTFormatSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 当前设置是否已修改
        /// </summary>
        private bool _isModified = false;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化PPT格式设置窗体
        /// </summary>
        public PPTFormatSettingsForm()
        {
            InitializeComponent();
            InitializeCustomControls();
            LoadCurrentSettings();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化自定义控件
        /// </summary>
        private void InitializeCustomControls()
        {
            try
            {
                // 设置窗体属性
                SetupFormProperties();

                // 初始化标签页内容
                InitializeParagraphTab();
                InitializeFontTab();
                InitializeThemeTab();
                InitializeMasterTab();
                InitializeLayoutTab();
                InitializeStyleTab();

                // 设置事件处理器
                SetupEventHandlers();

                // 设置标签页宽度均匀分布
                SetupTabWidths();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化控件时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置窗体属性
        /// </summary>
        private void SetupFormProperties()
        {
            // 设置窗体图标和标题
            this.Text = "PPT全局格式设置";
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;
            this.StartPosition = FormStartPosition.CenterParent;

            // 设置窗体大小
            this.Size = new Size(900, 650);
            this.MinimumSize = new Size(900, 650);
        }

        /// <summary>
        /// 设置标签页宽度均匀分布
        /// </summary>
        private void SetupTabWidths()
        {
            try
            {
                // 计算每个标签页的宽度
                int tabCount = tabControlMain.TabPages.Count;
                if (tabCount > 0)
                {
                    int totalWidth = tabControlMain.Width;
                    int tabWidth = totalWidth / tabCount;

                    // 设置标签页宽度
                    tabControlMain.SizeMode = TabSizeMode.Fixed;
                    tabControlMain.ItemSize = new Size(tabWidth - 2, tabControlMain.ItemSize.Height);
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不影响程序运行
                Console.WriteLine($"设置标签页宽度时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 按钮事件
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            btnApply.Click += BtnApply_Click;
            btnReset.Click += BtnReset_Click;

            // 窗体事件
            this.FormClosing += PPTFormatSettingsForm_FormClosing;
            this.Resize += PPTFormatSettingsForm_Resize;
        }

        #endregion

        #region 标签页初始化方法

        /// <summary>
        /// 初始化段落格式设置标签页
        /// </summary>
        private void InitializeParagraphTab()
        {
            try
            {
                CreateParagraphAlignmentGroup();
                CreateParagraphIndentGroup();
                CreateParagraphSpacingGroup();
                CreateParagraphOptionsGroup();
                CreateTextAlignmentGroup();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化段落格式设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化字体格式设置标签页
        /// </summary>
        private void InitializeFontTab()
        {
            try
            {
                CreateFontSelectionGroup();
                CreateFontStyleGroup();
                CreateFontEffectsGroup();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化字体格式设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化主题设置标签页
        /// </summary>
        private void InitializeThemeTab()
        {
            try
            {
                CreateBuiltInThemeGroup();
                CreateCustomThemeGroup();
                CreateThemeColorSchemeGroup();
                CreateThemeFontSchemeGroup();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化主题设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化母版设置标签页
        /// </summary>
        private void InitializeMasterTab()
        {
            CreateMasterSlideGroup();
        }

        /// <summary>
        /// 初始化布局设置标签页
        /// </summary>
        private void InitializeLayoutTab()
        {
            CreateSlideLayoutGroup();
        }

        /// <summary>
        /// 初始化样式设置标签页
        /// </summary>
        private void InitializeStyleTab()
        {
            CreateStyleGroup();
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                if (ApplySettings())
                {
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            if (CheckForUnsavedChanges())
            {
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
        }

        /// <summary>
        /// 应用按钮点击事件
        /// </summary>
        private void BtnApply_Click(object? sender, EventArgs e)
        {
            try
            {
                ApplySettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show("确定要重置所有设置为默认值吗？", "确认重置",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                ResetToDefaults();
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void PPTFormatSettingsForm_FormClosing(object? sender, FormClosingEventArgs e)
        {
            if (this.DialogResult != DialogResult.OK && !CheckForUnsavedChanges())
            {
                e.Cancel = true;
            }
        }

        /// <summary>
        /// 窗体大小改变事件
        /// </summary>
        private void PPTFormatSettingsForm_Resize(object? sender, EventArgs e)
        {
            SetupTabWidths();
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 加载当前设置
        /// </summary>
        private void LoadCurrentSettings()
        {
            try
            {
                // 加载当前的PPT格式设置
                // 这里将在后续实现具体的设置加载逻辑
                _isModified = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 应用设置
        /// </summary>
        /// <returns>是否成功应用</returns>
        private bool ApplySettings()
        {
            try
            {
                // 应用PPT格式设置
                // 这里将在后续实现具体的设置应用逻辑
                _isModified = false;
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 重置为默认设置
        /// </summary>
        private void ResetToDefaults()
        {
            try
            {
                // 重置所有设置为默认值
                // 这里将在后续实现具体的重置逻辑
                _isModified = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 检查未保存的更改
        /// </summary>
        /// <returns>是否可以继续操作</returns>
        private bool CheckForUnsavedChanges()
        {
            if (_isModified)
            {
                var result = MessageBox.Show("设置已修改但未保存，确定要放弃更改吗？", "确认",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                return result == DialogResult.Yes;
            }
            return true;
        }

        #endregion

        #region 段落格式设置控件创建方法

        /// <summary>
        /// 创建段落对齐方式组
        /// </summary>
        private void CreateParagraphAlignmentGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "对齐方式",
                Location = new Point(20, 20),
                Size = new Size(200, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 左对齐
            var rbLeft = new RadioButton
            {
                Text = "左对齐(&L)",
                Location = new Point(15, 25),
                Size = new Size(80, 20),
                Checked = true,
                Tag = "Left"
            };

            // 居中
            var rbCenter = new RadioButton
            {
                Text = "居中(&C)",
                Location = new Point(100, 25),
                Size = new Size(80, 20),
                Tag = "Center"
            };

            // 右对齐
            var rbRight = new RadioButton
            {
                Text = "右对齐(&R)",
                Location = new Point(15, 50),
                Size = new Size(80, 20),
                Tag = "Right"
            };

            // 两端对齐
            var rbJustify = new RadioButton
            {
                Text = "两端对齐(&J)",
                Location = new Point(100, 50),
                Size = new Size(80, 20),
                Tag = "Justify"
            };

            // 分散对齐
            var rbDistribute = new RadioButton
            {
                Text = "分散对齐(&D)",
                Location = new Point(15, 75),
                Size = new Size(80, 20),
                Tag = "Distribute"
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] { rbLeft, rbCenter, rbRight, rbJustify, rbDistribute });
            tabPageParagraph.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建段落缩进组
        /// </summary>
        private void CreateParagraphIndentGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "缩进",
                Location = new Point(240, 20),
                Size = new Size(280, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 文本之前缩进
            var lblBefore = new Label
            {
                Text = "文本之前:",
                Location = new Point(15, 30),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var numBefore = new NumericUpDown
            {
                Location = new Point(90, 28),
                Size = new Size(60, 23),
                DecimalPlaces = 1,
                Minimum = 0,
                Maximum = 100,
                Value = 0,
                TextAlign = HorizontalAlignment.Center
            };

            var lblBeforeUnit = new Label
            {
                Text = "厘米",
                Location = new Point(155, 30),
                Size = new Size(30, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 特殊缩进
            var lblSpecial = new Label
            {
                Text = "特殊缩进:",
                Location = new Point(15, 60),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var cmbSpecial = new ComboBox
            {
                Location = new Point(90, 58),
                Size = new Size(80, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbSpecial.Items.AddRange(new string[] { "无", "首行缩进", "悬挂缩进" });
            cmbSpecial.SelectedIndex = 0;

            var numSpecial = new NumericUpDown
            {
                Location = new Point(180, 58),
                Size = new Size(60, 23),
                DecimalPlaces = 1,
                Minimum = 0,
                Maximum = 10,
                Value = 2,
                TextAlign = HorizontalAlignment.Center,
                Enabled = false
            };

            var lblSpecialUnit = new Label
            {
                Text = "字符",
                Location = new Point(245, 60),
                Size = new Size(30, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 特殊缩进类型变更事件
            cmbSpecial.SelectedIndexChanged += (s, e) =>
            {
                numSpecial.Enabled = cmbSpecial.SelectedIndex > 0;
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                lblBefore, numBefore, lblBeforeUnit,
                lblSpecial, cmbSpecial, numSpecial, lblSpecialUnit
            });
            tabPageParagraph.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建段落间距组
        /// </summary>
        private void CreateParagraphSpacingGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "间距",
                Location = new Point(540, 20),
                Size = new Size(280, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 段前间距
            var lblBefore = new Label
            {
                Text = "段前:",
                Location = new Point(15, 30),
                Size = new Size(50, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var numBefore = new NumericUpDown
            {
                Location = new Point(70, 28),
                Size = new Size(60, 23),
                DecimalPlaces = 1,
                Minimum = 0,
                Maximum = 100,
                Value = 0,
                TextAlign = HorizontalAlignment.Center
            };

            var lblBeforeUnit = new Label
            {
                Text = "磅",
                Location = new Point(135, 30),
                Size = new Size(20, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 段后间距
            var lblAfter = new Label
            {
                Text = "段后:",
                Location = new Point(160, 30),
                Size = new Size(50, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var numAfter = new NumericUpDown
            {
                Location = new Point(210, 28),
                Size = new Size(60, 23),
                DecimalPlaces = 1,
                Minimum = 0,
                Maximum = 100,
                Value = 0,
                TextAlign = HorizontalAlignment.Center
            };

            var lblAfterUnit = new Label
            {
                Text = "磅",
                Location = new Point(275, 30),
                Size = new Size(20, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 行距
            var lblLineSpacing = new Label
            {
                Text = "行距:",
                Location = new Point(15, 65),
                Size = new Size(50, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var cmbLineSpacing = new ComboBox
            {
                Location = new Point(70, 63),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbLineSpacing.Items.AddRange(new string[] { "单倍行距", "1.5倍行距", "2倍行距", "多倍行距", "固定值" });
            cmbLineSpacing.SelectedIndex = 0;

            var numLineSpacing = new NumericUpDown
            {
                Location = new Point(180, 63),
                Size = new Size(60, 23),
                DecimalPlaces = 1,
                Minimum = 0.1m,
                Maximum = 10,
                Value = 1,
                TextAlign = HorizontalAlignment.Center,
                Enabled = false
            };

            // 行距类型变更事件
            cmbLineSpacing.SelectedIndexChanged += (s, e) =>
            {
                numLineSpacing.Enabled = cmbLineSpacing.SelectedIndex >= 3;
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                lblBefore, numBefore, lblBeforeUnit,
                lblAfter, numAfter, lblAfterUnit,
                lblLineSpacing, cmbLineSpacing, numLineSpacing
            });
            tabPageParagraph.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建段落选项组
        /// </summary>
        private void CreateParagraphOptionsGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "中文排版选项",
                Location = new Point(20, 160),
                Size = new Size(400, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 按中文习惯控制中文首尾字符
            var chkChineseControl = new CheckBox
            {
                Text = "按中文习惯控制中文首尾字符(&H)",
                Location = new Point(15, 25),
                Size = new Size(250, 20),
                Checked = true
            };

            // 允许西文在单词中间换行
            var chkWordWrap = new CheckBox
            {
                Text = "允许西文在单词中间换行(&W)",
                Location = new Point(15, 50),
                Size = new Size(250, 20),
                Checked = false
            };

            // 允许标点移除边界
            var chkPunctuationBoundary = new CheckBox
            {
                Text = "允许标点移除边界(&P)",
                Location = new Point(15, 75),
                Size = new Size(250, 20),
                Checked = false
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] { chkChineseControl, chkWordWrap, chkPunctuationBoundary });
            tabPageParagraph.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建文本对齐方式组
        /// </summary>
        private void CreateTextAlignmentGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "文本对齐方式",
                Location = new Point(440, 160),
                Size = new Size(200, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 自动
            var rbAuto = new RadioButton
            {
                Text = "自动(&A)",
                Location = new Point(15, 25),
                Size = new Size(80, 20),
                Checked = true,
                Tag = "Auto"
            };

            // 居中
            var rbCenter = new RadioButton
            {
                Text = "居中(&C)",
                Location = new Point(100, 25),
                Size = new Size(80, 20),
                Tag = "Center"
            };

            // 基线
            var rbBaseline = new RadioButton
            {
                Text = "基线(&B)",
                Location = new Point(15, 50),
                Size = new Size(80, 20),
                Tag = "Baseline"
            };

            // 底部
            var rbBottom = new RadioButton
            {
                Text = "底部(&T)",
                Location = new Point(100, 50),
                Size = new Size(80, 20),
                Tag = "Bottom"
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] { rbAuto, rbCenter, rbBaseline, rbBottom });
            tabPageParagraph.Controls.Add(groupBox);
        }

        #endregion

        #region 字体格式设置控件创建方法

        /// <summary>
        /// 创建字体选择组
        /// </summary>
        private void CreateFontSelectionGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "字体选择",
                Location = new Point(20, 20),
                Size = new Size(400, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 中文字体
            var lblChineseFont = new Label
            {
                Text = "中文字体:",
                Location = new Point(15, 30),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var cmbChineseFont = new ComboBox
            {
                Location = new Point(90, 28),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            // 添加常用中文字体
            cmbChineseFont.Items.AddRange(new string[] {
                "宋体", "黑体", "楷体", "仿宋", "微软雅黑", "华文宋体", "华文黑体", "华文楷体", "方正舒体", "方正姚体"
            });
            cmbChineseFont.SelectedIndex = 0;

            // 西文字体
            var lblEnglishFont = new Label
            {
                Text = "西文字体:",
                Location = new Point(15, 60),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var cmbEnglishFont = new ComboBox
            {
                Location = new Point(90, 58),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            // 添加常用西文字体
            cmbEnglishFont.Items.AddRange(new string[] {
                "Arial", "Times New Roman", "Calibri", "Verdana", "Tahoma", "Georgia", "Comic Sans MS", "Impact"
            });
            cmbEnglishFont.SelectedIndex = 0;

            // 字体大小
            var lblFontSize = new Label
            {
                Text = "字体大小:",
                Location = new Point(260, 30),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var numFontSize = new NumericUpDown
            {
                Location = new Point(330, 28),
                Size = new Size(60, 23),
                Minimum = 8,
                Maximum = 72,
                Value = 12,
                TextAlign = HorizontalAlignment.Center
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                lblChineseFont, cmbChineseFont,
                lblEnglishFont, cmbEnglishFont,
                lblFontSize, numFontSize
            });
            tabPageFont.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建字体样式组
        /// </summary>
        private void CreateFontStyleGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "字体样式",
                Location = new Point(440, 20),
                Size = new Size(380, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 字体样式
            var lblFontStyle = new Label
            {
                Text = "字体样式:",
                Location = new Point(15, 30),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var cmbFontStyle = new ComboBox
            {
                Location = new Point(90, 28),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbFontStyle.Items.AddRange(new string[] { "常规", "倾斜", "加粗", "倾斜加粗" });
            cmbFontStyle.SelectedIndex = 0;

            // 字体颜色
            var lblFontColor = new Label
            {
                Text = "字体颜色:",
                Location = new Point(200, 30),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var btnFontColor = new Button
            {
                Location = new Point(275, 28),
                Size = new Size(80, 23),
                Text = "选择颜色",
                BackColor = Color.Black,
                ForeColor = Color.White
            };

            // 下划线线型
            var lblUnderline = new Label
            {
                Text = "下划线:",
                Location = new Point(15, 60),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var cmbUnderline = new ComboBox
            {
                Location = new Point(90, 58),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbUnderline.Items.AddRange(new string[] { "无", "单下划线", "双下划线", "粗下划线", "点下划线", "虚线下划线" });
            cmbUnderline.SelectedIndex = 0;

            // 下划线颜色
            var lblUnderlineColor = new Label
            {
                Text = "下划线颜色:",
                Location = new Point(200, 60),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var btnUnderlineColor = new Button
            {
                Location = new Point(275, 58),
                Size = new Size(80, 23),
                Text = "选择颜色",
                BackColor = Color.Black,
                ForeColor = Color.White,
                Enabled = false
            };

            // 下划线类型变更事件
            cmbUnderline.SelectedIndexChanged += (s, e) =>
            {
                btnUnderlineColor.Enabled = cmbUnderline.SelectedIndex > 0;
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                lblFontStyle, cmbFontStyle,
                lblFontColor, btnFontColor,
                lblUnderline, cmbUnderline,
                lblUnderlineColor, btnUnderlineColor
            });
            tabPageFont.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建字体效果组
        /// </summary>
        private void CreateFontEffectsGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "文字效果",
                Location = new Point(20, 160),
                Size = new Size(400, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 删除线
            var chkStrikethrough = new CheckBox
            {
                Text = "删除线(&S)",
                Location = new Point(15, 25),
                Size = new Size(80, 20),
                Checked = false
            };

            // 双删除线
            var chkDoubleStrikethrough = new CheckBox
            {
                Text = "双删除线(&D)",
                Location = new Point(100, 25),
                Size = new Size(100, 20),
                Checked = false
            };

            // 上标
            var chkSuperscript = new CheckBox
            {
                Text = "上标(&U)",
                Location = new Point(210, 25),
                Size = new Size(80, 20),
                Checked = false
            };

            // 下标
            var chkSubscript = new CheckBox
            {
                Text = "下标(&B)",
                Location = new Point(300, 25),
                Size = new Size(80, 20),
                Checked = false
            };

            // 上标和下标互斥
            chkSuperscript.CheckedChanged += (s, e) =>
            {
                if (chkSuperscript.Checked)
                    chkSubscript.Checked = false;
            };

            chkSubscript.CheckedChanged += (s, e) =>
            {
                if (chkSubscript.Checked)
                    chkSuperscript.Checked = false;
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                chkStrikethrough, chkDoubleStrikethrough, chkSuperscript, chkSubscript
            });
            tabPageFont.Controls.Add(groupBox);
        }

        #endregion

        #region 主题设置控件创建方法

        /// <summary>
        /// 创建内置主题组
        /// </summary>
        private void CreateBuiltInThemeGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "应用内置主题",
                Location = new Point(20, 20),
                Size = new Size(400, 180),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 内置主题列表
            var listThemes = new ListBox
            {
                Location = new Point(15, 25),
                Size = new Size(280, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 添加常用内置主题
            listThemes.Items.AddRange(new string[] {
                "Office 主题",
                "Facet 主题",
                "Ion 主题",
                "Retrospect 主题",
                "Slice 主题",
                "Wisp 主题",
                "Berlin 主题",
                "Celestial 主题",
                "Dividend 主题",
                "Droplet 主题"
            });
            listThemes.SelectedIndex = 0;

            // 应用主题按钮
            var btnApplyTheme = new Button
            {
                Text = "应用主题(&A)",
                Location = new Point(305, 25),
                Size = new Size(80, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 预览主题按钮
            var btnPreviewTheme = new Button
            {
                Text = "预览主题(&P)",
                Location = new Point(305, 65),
                Size = new Size(80, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] { listThemes, btnApplyTheme, btnPreviewTheme });
            tabPageTheme.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建自定义主题组
        /// </summary>
        private void CreateCustomThemeGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "自定义主题",
                Location = new Point(440, 20),
                Size = new Size(380, 180),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 创建新主题
            var btnCreateTheme = new Button
            {
                Text = "创建新主题(&N)",
                Location = new Point(15, 30),
                Size = new Size(100, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 编辑当前主题
            var btnEditTheme = new Button
            {
                Text = "编辑当前主题(&E)",
                Location = new Point(125, 30),
                Size = new Size(100, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 保存主题
            var btnSaveTheme = new Button
            {
                Text = "保存主题(&S)",
                Location = new Point(235, 30),
                Size = new Size(100, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 导入主题
            var btnImportTheme = new Button
            {
                Text = "导入主题(&I)",
                Location = new Point(15, 70),
                Size = new Size(100, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 导出主题
            var btnExportTheme = new Button
            {
                Text = "导出主题(&X)",
                Location = new Point(125, 70),
                Size = new Size(100, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 删除主题
            var btnDeleteTheme = new Button
            {
                Text = "删除主题(&D)",
                Location = new Point(235, 70),
                Size = new Size(100, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                btnCreateTheme, btnEditTheme, btnSaveTheme,
                btnImportTheme, btnExportTheme, btnDeleteTheme
            });
            tabPageTheme.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建主题颜色方案组
        /// </summary>
        private void CreateThemeColorSchemeGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "主题颜色方案",
                Location = new Point(20, 220),
                Size = new Size(400, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 颜色方案列表
            var cmbColorScheme = new ComboBox
            {
                Location = new Point(15, 30),
                Size = new Size(200, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 添加常用颜色方案
            cmbColorScheme.Items.AddRange(new string[] {
                "Office 颜色方案",
                "蓝色方案",
                "绿色方案",
                "橙色方案",
                "红色方案",
                "紫色方案",
                "灰色方案",
                "彩虹方案"
            });
            cmbColorScheme.SelectedIndex = 0;

            // 应用颜色方案按钮
            var btnApplyColorScheme = new Button
            {
                Text = "应用方案(&A)",
                Location = new Point(230, 28),
                Size = new Size(80, 27),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 自定义颜色方案按钮
            var btnCustomColorScheme = new Button
            {
                Text = "自定义方案(&C)",
                Location = new Point(320, 28),
                Size = new Size(80, 27),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 颜色预览面板
            var panelColorPreview = new Panel
            {
                Location = new Point(15, 65),
                Size = new Size(370, 40),
                BorderStyle = BorderStyle.FixedSingle
            };

            // 添加颜色预览块
            for (int i = 0; i < 8; i++)
            {
                var colorBlock = new Panel
                {
                    Location = new Point(i * 45 + 5, 5),
                    Size = new Size(40, 30),
                    BorderStyle = BorderStyle.FixedSingle,
                    BackColor = GetPreviewColor(i)
                };
                panelColorPreview.Controls.Add(colorBlock);
            }

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                cmbColorScheme, btnApplyColorScheme, btnCustomColorScheme, panelColorPreview
            });
            tabPageTheme.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建主题字体方案组
        /// </summary>
        private void CreateThemeFontSchemeGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "主题字体方案",
                Location = new Point(440, 220),
                Size = new Size(380, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 字体方案列表
            var cmbFontScheme = new ComboBox
            {
                Location = new Point(15, 30),
                Size = new Size(200, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 添加常用字体方案
            cmbFontScheme.Items.AddRange(new string[] {
                "Office 字体方案",
                "经典字体方案",
                "现代字体方案",
                "优雅字体方案",
                "简洁字体方案",
                "艺术字体方案"
            });
            cmbFontScheme.SelectedIndex = 0;

            // 应用字体方案按钮
            var btnApplyFontScheme = new Button
            {
                Text = "应用方案(&A)",
                Location = new Point(230, 28),
                Size = new Size(80, 27),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 自定义字体方案按钮
            var btnCustomFontScheme = new Button
            {
                Text = "自定义方案(&C)",
                Location = new Point(320, 28),
                Size = new Size(80, 27),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 字体预览标签
            var lblFontPreview = new Label
            {
                Text = "标题字体: 微软雅黑\n正文字体: 宋体",
                Location = new Point(15, 65),
                Size = new Size(350, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular),
                BorderStyle = BorderStyle.FixedSingle,
                TextAlign = ContentAlignment.MiddleLeft
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                cmbFontScheme, btnApplyFontScheme, btnCustomFontScheme, lblFontPreview
            });
            tabPageTheme.Controls.Add(groupBox);
        }

        /// <summary>
        /// 获取预览颜色
        /// </summary>
        /// <param name="index">颜色索引</param>
        /// <returns>颜色</returns>
        private Color GetPreviewColor(int index)
        {
            Color[] colors = {
                Color.FromArgb(68, 114, 196),   // 蓝色
                Color.FromArgb(237, 125, 49),   // 橙色
                Color.FromArgb(165, 165, 165),  // 灰色
                Color.FromArgb(255, 192, 0),    // 黄色
                Color.FromArgb(91, 155, 213),   // 浅蓝色
                Color.FromArgb(112, 173, 71),   // 绿色
                Color.FromArgb(158, 72, 14),    // 棕色
                Color.FromArgb(99, 99, 99)      // 深灰色
            };
            return index < colors.Length ? colors[index] : Color.Gray;
        }

        #endregion

        #region 母版、布局和样式设置控件创建方法

        /// <summary>
        /// 创建母版幻灯片组
        /// </summary>
        private void CreateMasterSlideGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "母版设置",
                Location = new Point(20, 20),
                Size = new Size(800, 300),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 幻灯片母版
            var btnSlideMaster = new Button
            {
                Text = "幻灯片母版(&S)",
                Location = new Point(20, 30),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 标题母版
            var btnTitleMaster = new Button
            {
                Text = "标题母版(&T)",
                Location = new Point(160, 30),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 备注母版
            var btnNotesMaster = new Button
            {
                Text = "备注母版(&N)",
                Location = new Point(300, 30),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 讲义母版
            var btnHandoutMaster = new Button
            {
                Text = "讲义母版(&H)",
                Location = new Point(440, 30),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblInfo = new Label
            {
                Text = "母版设置功能允许您定义演示文稿的整体外观和格式。\n" +
                       "• 幻灯片母版：控制普通幻灯片的布局和格式\n" +
                       "• 标题母版：控制标题幻灯片的布局和格式\n" +
                       "• 备注母版：控制备注页面的布局和格式\n" +
                       "• 讲义母版：控制打印讲义的布局和格式",
                Location = new Point(20, 90),
                Size = new Size(760, 180),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                btnSlideMaster, btnTitleMaster, btnNotesMaster, btnHandoutMaster, lblInfo
            });
            tabPageMaster.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建幻灯片布局组
        /// </summary>
        private void CreateSlideLayoutGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "幻灯片布局",
                Location = new Point(20, 20),
                Size = new Size(800, 300),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 标题幻灯片布局
            var btnTitleLayout = new Button
            {
                Text = "标题幻灯片布局(&T)",
                Location = new Point(20, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 内容布局
            var btnContentLayout = new Button
            {
                Text = "内容布局(&C)",
                Location = new Point(180, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 两栏布局
            var btnTwoColumnLayout = new Button
            {
                Text = "两栏布局(&W)",
                Location = new Point(340, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 图片布局
            var btnPictureLayout = new Button
            {
                Text = "图片布局(&P)",
                Location = new Point(500, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 自定义布局
            var btnCustomLayout = new Button
            {
                Text = "自定义布局(&U)",
                Location = new Point(660, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblInfo = new Label
            {
                Text = "布局设置功能允许您选择和自定义幻灯片的内容排列方式。\n" +
                       "• 标题幻灯片布局：用于演示文稿的开头和章节标题\n" +
                       "• 内容布局：包含标题和内容占位符的标准布局\n" +
                       "• 两栏布局：将内容分为两列显示\n" +
                       "• 图片布局：专门用于展示图片的布局\n" +
                       "• 自定义布局：根据需要创建特殊的布局样式",
                Location = new Point(20, 90),
                Size = new Size(760, 180),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                btnTitleLayout, btnContentLayout, btnTwoColumnLayout, btnPictureLayout, btnCustomLayout, lblInfo
            });
            tabPageLayout.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建样式组
        /// </summary>
        private void CreateStyleGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "样式设置",
                Location = new Point(20, 20),
                Size = new Size(800, 300),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 形状样式
            var btnShapeStyle = new Button
            {
                Text = "形状样式(&S)",
                Location = new Point(20, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 文本样式
            var btnTextStyle = new Button
            {
                Text = "文本样式(&T)",
                Location = new Point(180, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 表格样式
            var btnTableStyle = new Button
            {
                Text = "表格样式(&B)",
                Location = new Point(340, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 图表样式
            var btnChartStyle = new Button
            {
                Text = "图表样式(&H)",
                Location = new Point(500, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            var lblInfo = new Label
            {
                Text = "样式设置功能允许您定义各种元素的外观样式。\n" +
                       "• 形状样式：设置形状的填充、边框、效果等样式\n" +
                       "• 文本样式：设置文本的字体、颜色、格式等样式\n" +
                       "• 表格样式：设置表格的边框、填充、字体等样式\n" +
                       "• 图表样式：设置图表的颜色、字体、效果等样式",
                Location = new Point(20, 90),
                Size = new Size(760, 180),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                btnShapeStyle, btnTextStyle, btnTableStyle, btnChartStyle, lblInfo
            });
            tabPageStyle.Controls.Add(groupBox);
        }

        #endregion
    }
}
