using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Services
{
    /// <summary>
    /// 文件处理服务
    /// </summary>
    public class FileProcessingService
    {
        private static readonly Lazy<FileProcessingService> _instance =
            new Lazy<FileProcessingService>(() => new FileProcessingService());

        public static FileProcessingService Instance => _instance.Value;

        private CancellationTokenSource? _cancellationTokenSource;
        private bool _isProcessing = false;

        /// <summary>
        /// 处理进度事件
        /// </summary>
        public event EventHandler<ProcessProgressEventArgs>? ProgressChanged;

        /// <summary>
        /// 处理完成事件
        /// </summary>
        public event EventHandler<ProcessCompletedEventArgs>? ProcessCompleted;

        /// <summary>
        /// 文件处理事件
        /// </summary>
        public event EventHandler<FileProcessedEventArgs>? FileProcessed;

        private FileProcessingService() { }

        /// <summary>
        /// 获取指定目录下的PowerPoint文件
        /// </summary>
        public List<string> GetPowerPointFiles(string sourcePath, bool includeSubfolders, List<string> supportedFormats)
        {
            var files = new List<string>();

            if (!Directory.Exists(sourcePath))
                return files;

            try
            {
                var searchOption = includeSubfolders ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;

                foreach (var format in supportedFormats)
                {
                    var pattern = $"*{format}";
                    var formatFiles = Directory.GetFiles(sourcePath, pattern, searchOption);
                    files.AddRange(formatFiles);
                }

                // 去重并排序
                files = files.Distinct().OrderBy(f => f).ToList();
            }
            catch (Exception ex)
            {
                LogError($"扫描文件时出错: {ex.Message}");
            }

            return files;
        }

        /// <summary>
        /// 开始处理文件
        /// </summary>
        public async Task StartProcessingAsync(ProcessingOptions options)
        {
            if (_isProcessing)
            {
                throw new InvalidOperationException("已有处理任务在进行中");
            }

            _isProcessing = true;
            _cancellationTokenSource = new CancellationTokenSource();

            try
            {
                await ProcessFilesAsync(options, _cancellationTokenSource.Token);
            }
            finally
            {
                _isProcessing = false;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        /// <summary>
        /// 停止处理
        /// </summary>
        public void StopProcessing()
        {
            _cancellationTokenSource?.Cancel();
        }

        /// <summary>
        /// 是否正在处理
        /// </summary>
        public bool IsProcessing => _isProcessing;

        /// <summary>
        /// 处理文件
        /// </summary>
        private async Task ProcessFilesAsync(ProcessingOptions options, CancellationToken cancellationToken)
        {
            var startTime = DateTime.Now;
            var files = GetPowerPointFiles(options.SourcePath, options.IncludeSubfolders, options.SupportedFormats);

            var stats = new ProcessingStats
            {
                TotalFiles = files.Count,
                StartTime = startTime
            };

            OnProgressChanged(new ProcessProgressEventArgs(0, stats));

            if (files.Count == 0)
            {
                OnProcessCompleted(new ProcessCompletedEventArgs(stats, "没有找到符合条件的文件"));
                return;
            }

            // 创建输出目录
            if (!Directory.Exists(options.OutputPath))
            {
                Directory.CreateDirectory(options.OutputPath);
            }

            // 分批处理
            var batches = CreateBatches(files, options.BatchSize);
            var semaphore = new SemaphoreSlim(options.ThreadCount, options.ThreadCount);

            var tasks = batches.Select(async batch =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    await ProcessBatchAsync(batch, options, stats, cancellationToken);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);

            stats.EndTime = DateTime.Now;
            OnProcessCompleted(new ProcessCompletedEventArgs(stats, "处理完成"));
        }

        /// <summary>
        /// 分批处理文件
        /// </summary>
        private List<List<string>> CreateBatches(List<string> files, int batchSize)
        {
            var batches = new List<List<string>>();

            for (int i = 0; i < files.Count; i += batchSize)
            {
                var batch = files.Skip(i).Take(batchSize).ToList();
                batches.Add(batch);
            }

            return batches;
        }

        /// <summary>
        /// 处理批次
        /// </summary>
        private async Task ProcessBatchAsync(List<string> batch, ProcessingOptions options,
            ProcessingStats stats, CancellationToken cancellationToken)
        {
            foreach (var file in batch)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                await ProcessSingleFileAsync(file, options, stats, cancellationToken);
            }
        }

        /// <summary>
        /// 处理单个文件
        /// </summary>
        private async Task ProcessSingleFileAsync(string sourceFile, ProcessingOptions options,
            ProcessingStats stats, CancellationToken cancellationToken)
        {
            var fileName = Path.GetFileName(sourceFile);
            var retryCount = 0;
            bool success = false;

            while (retryCount <= options.RetryCount && !success && !cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // 计算目标文件路径
                    var targetFile = CalculateTargetPath(sourceFile, options);

                    // 确保目标目录存在
                    var targetDir = Path.GetDirectoryName(targetFile);
                    if (!string.IsNullOrEmpty(targetDir) && !Directory.Exists(targetDir))
                    {
                        Directory.CreateDirectory(targetDir);
                    }

                    // 复制或移动文件
                    if (options.ProcessSourceDirectly)
                    {
                        // 直接处理源文件
                        await ProcessPowerPointFileAsync(sourceFile, cancellationToken);
                    }
                    else
                    {
                        // 先复制/移动文件，再处理
                        if (options.CopyFiles)
                        {
                            File.Copy(sourceFile, targetFile, true);
                        }
                        else
                        {
                            File.Move(sourceFile, targetFile);
                        }

                        await ProcessPowerPointFileAsync(targetFile, cancellationToken);
                    }

                    success = true;
                    lock (stats)
                    {
                        stats.SuccessCount++;
                    }

                    OnFileProcessed(new FileProcessedEventArgs(fileName, true, null));
                }
                catch (Exception ex)
                {
                    retryCount++;
                    LogError($"处理文件 {fileName} 失败 (第{retryCount}次尝试): {ex.Message}");

                    if (retryCount > options.RetryCount)
                    {
                        lock (stats)
                        {
                            stats.FailureCount++;
                        }
                        OnFileProcessed(new FileProcessedEventArgs(fileName, false, ex.Message));
                    }
                    else
                    {
                        // 等待一段时间后重试
                        await Task.Delay(1000, cancellationToken);
                    }
                }
            }

            // 更新进度
            var processed = stats.SuccessCount + stats.FailureCount;
            var progress = (int)((double)processed / stats.TotalFiles * 100);
            OnProgressChanged(new ProcessProgressEventArgs(progress, stats));
        }

        /// <summary>
        /// 计算目标文件路径
        /// </summary>
        private string CalculateTargetPath(string sourceFile, ProcessingOptions options)
        {
            var fileName = Path.GetFileName(sourceFile);

            if (options.KeepDirectoryStructure)
            {
                var relativePath = Path.GetRelativePath(options.SourcePath, sourceFile);
                return Path.Combine(options.OutputPath, relativePath);
            }
            else
            {
                return Path.Combine(options.OutputPath, fileName);
            }
        }

        /// <summary>
        /// 处理PowerPoint文件（占位符方法）
        /// </summary>
        private async Task ProcessPowerPointFileAsync(string filePath, CancellationToken cancellationToken)
        {
            // TODO: 在这里实现具体的PowerPoint文件处理逻辑
            // 使用Aspose.Slides API进行文件处理

            await Task.Delay(100, cancellationToken); // 模拟处理时间
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        private void LogError(string message)
        {
            try
            {
                var logFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Log",
                    $"Error_{DateTime.Now:yyyyMMdd}.log");
                var logDir = Path.GetDirectoryName(logFile);

                if (!string.IsNullOrEmpty(logDir) && !Directory.Exists(logDir))
                {
                    Directory.CreateDirectory(logDir);
                }

                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}\n";
                File.AppendAllText(logFile, logEntry);
            }
            catch
            {
                // 忽略日志记录错误
            }
        }

        /// <summary>
        /// 触发进度变更事件
        /// </summary>
        protected virtual void OnProgressChanged(ProcessProgressEventArgs e)
        {
            ProgressChanged?.Invoke(this, e);
        }

        /// <summary>
        /// 触发处理完成事件
        /// </summary>
        protected virtual void OnProcessCompleted(ProcessCompletedEventArgs e)
        {
            ProcessCompleted?.Invoke(this, e);
        }

        /// <summary>
        /// 触发文件处理事件
        /// </summary>
        protected virtual void OnFileProcessed(FileProcessedEventArgs e)
        {
            FileProcessed?.Invoke(this, e);
        }
    }
}
