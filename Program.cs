using System;
using System.IO;
using System.Windows.Forms;
using Aspose.Slides;

namespace PPTPiliangChuli
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // 初始化应用程序
            Application.SetHighDpiMode(HighDpiMode.SystemAware);
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                // 设置Aspose.Slides许可证
                SetAsposeSlicesLicense();

                // 创建必要的目录
                CreateDirectories();

                // 启动主窗体
                Application.Run(new MainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"程序启动失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置Aspose.Slides许可证
        /// </summary>
        private static void SetAsposeSlicesLicense()
        {
            try
            {
                string licenseFile = Path.Combine(Application.StartupPath, "Aspose.Total.NET.lic");
                if (File.Exists(licenseFile))
                {
                    License license = new License();
                    license.SetLicense(licenseFile);
                }
            }
            catch (Exception ex)
            {
                // 许可证加载失败，继续使用试用版
                MessageBox.Show($"许可证加载失败，将使用试用版：{ex.Message}", "警告", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 创建必要的目录
        /// </summary>
        private static void CreateDirectories()
        {
            string appPath = Application.StartupPath;
            
            // 创建配置目录
            string configPath = Path.Combine(appPath, "Config");
            if (!Directory.Exists(configPath))
                Directory.CreateDirectory(configPath);

            // 创建日志目录
            string logPath = Path.Combine(appPath, "Log");
            if (!Directory.Exists(logPath))
                Directory.CreateDirectory(logPath);
        }
    }
}
