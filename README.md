# PPT批量处理工具

基于Aspose.Slides的PowerPoint文件批量处理软件，使用.NET 6.0和Windows Forms开发。

## 项目状态

### 已完成功能

#### 1. 主界面UI ✅
- **菜单栏**：文件、操作、设置、帮助
- **路径设置区域**：
  - 源目录选择（支持浏览按钮）
  - 输出目录选择（支持浏览按钮）
  - 包含子目录选项
  - 保持目录结构选项
- **处理设置区域**（优化为两行布局）：
  - **第一行**：支持格式设定按钮、线程数、重试次数、批处理数量
  - **第二行**：冲突处理方式（复制/移动）、直接处理源文件、全选/取消全选按钮
  - 线程数设置（1-16）
  - 重试次数设置（0-10）
  - 批处理数量设置（1-1000）

#### 2. 功能选择区域 ✅
- **3x3网格布局**，包含9个功能模块：
  1. 页面设置
  2. 内容删除设置
  3. 内容替换设置
  4. PPT格式设置
  5. 匹配段落格式
  6. 页眉页脚设置
  7. 文档属性
  8. 文件名替换
  9. PPT格式转换
- 每个功能都有独立的启用复选框
- **优化布局**：按钮自适应宽度，合理的左右边距

#### 3. 操作控制区域 ✅
- **3x3网格布局**，包含9个操作按钮：
  1. 开始处理
  2. 定时处理
  3. 停止处理
  4. 日志设置
  5. 清空日志
  6. 导出配置
  7. 导入配置
  8. 打开源目录
  9. 打开输出目录
- **优化布局**：按钮自适应宽度，统一的边距设置

#### 4. 统计信息区域 ✅
- 处理统计信息显示
- 进度条
- 处理速度和耗时显示

#### 5. 配置管理系统 ✅
- **配置自动保存/加载**：所有用户设置自动保存到Config目录
- **配置导出/导入**：支持配置的备份和恢复
- **JSON格式配置文件**：易于编辑和管理
- **配置分类管理**：
  - 路径设置
  - 处理设置
  - 功能启用状态
  - 日志设置
  - 窗体设置

#### 6. 基础架构 ✅
- **项目结构**：标准的Visual Studio项目结构
- **Aspose.Slides集成**：已配置Aspose.Slides.dll引用
- **许可证支持**：自动加载Aspose.Total.NET.lic
- **目录结构**：自动创建Config和Log目录
- **错误处理**：基础的异常处理机制

### 待实现功能

#### 1. 功能设置窗体
- 每个功能模块的详细设置窗体
- 根据需求文档实现各功能的具体参数设置

#### 2. 核心处理逻辑
- 文件扫描和过滤
- 多线程处理框架
- Aspose.Slides API调用
- 错误重试机制

#### 3. 日志系统
- 详细的日志记录
- 日志级别控制
- 日志文件管理

#### 4. 定时处理功能
- 一次性定时
- 周期性定时
- 倒计时功能

## 技术架构

### 项目结构
```
PPTPiliangChuli/
├── Program.cs              # 程序入口点
├── MainForm.cs             # 主窗体
├── MainForm.Designer.cs    # 主窗体设计器
├── Models/                 # 数据模型
│   └── AppConfig.cs        # 应用配置模型
├── Services/               # 业务服务
│   └── ConfigService.cs    # 配置管理服务
├── Forms/                  # 功能设置窗体（待创建）
├── Utils/                  # 工具类（待创建）
├── Config/                 # 配置文件目录
└── Log/                    # 日志文件目录
```

### 技术栈
- **.NET 6.0**：目标框架
- **Windows Forms**：UI框架
- **Aspose.Slides 25.4.0.0**：PPT处理核心库
- **System.Text.Json**：配置序列化
- **Newtonsoft.Json**：备用JSON处理

### 配置系统
- **自动保存**：窗体关闭时自动保存所有设置
- **自动加载**：程序启动时自动加载上次的设置
- **备份机制**：导入配置前自动备份当前配置
- **容错处理**：配置文件损坏时使用默认配置

## 使用说明

### 编译运行
1. 确保安装了.NET 6.0 SDK
2. 在项目根目录执行：
   ```bash
   dotnet build
   dotnet run
   ```

### 界面操作
1. **设置路径**：选择源目录和输出目录
2. **配置参数**：设置线程数、重试次数等处理参数
3. **选择功能**：勾选需要的处理功能
4. **开始处理**：点击"开始处理"按钮

### 配置管理
- **导出配置**：将当前所有设置导出为备份
- **导入配置**：从备份恢复设置
- **自动保存**：所有设置在程序关闭时自动保存

## 开发计划

### 第一阶段：核心功能实现
1. 实现页面设置功能窗体
2. 实现内容删除功能窗体
3. 实现基础的文件处理逻辑

### 第二阶段：高级功能
1. 实现内容替换功能
2. 实现格式设置功能
3. 完善日志系统

### 第三阶段：优化完善
1. 实现定时处理功能
2. 性能优化
3. 用户体验改进

## 注意事项

1. **许可证**：需要有效的Aspose.Total.NET.lic许可证文件
2. **依赖项**：确保Aspose.Slides.dll在项目根目录
3. **权限**：程序需要文件读写权限
4. **兼容性**：支持Windows 10及以上版本

## 更新日志

### v1.1.0 (当前版本)
- ✅ 优化界面布局和美化
- ✅ 处理设置区域分两行显示
- ✅ 功能选择和操作控制区域按钮自适应宽度
- ✅ 添加支持格式设定按钮
- ✅ 改进按钮边距和视觉效果

### v1.0.0
- ✅ 完成主界面UI设计
- ✅ 实现配置管理系统
- ✅ 集成Aspose.Slides基础框架
- ✅ 实现基础的用户交互功能
