  PPTPiliangChuli 成功，出现 19 警告 (0.5) → bin\Debug\net6.0-windows\PPTPiliangChuli.dll
    D:\Cursor\PPTPiliangChuli\Forms\CustomColorReplacementRuleForm.cs(30,81): warning CS8625: 无法将 null 字面量转换为非 null 的引用类型。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,77): warning CS8625: 无法将 null 字面量转换为非 null 的引用类型。
    D:\Cursor\PPTPiliangChuli\Forms\FontNameReplacementRuleForm.cs(26,75): warning CS8625: 无法将 null 字面量转换为非 null 的引用类型。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(26,69): warning CS8625: 无法将 null 字面量转换为非 null 的引用类型。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,79): warning CS8625: 无法将 null 字面量转换为非 null 的引用类型。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(29,73): warning CS8625: 无法将 null 字面量转换为非 null 的引用类型。
    D:\Cursor\PPTPiliangChuli\Forms\TextReplacementRuleForm.cs(15,67): warning CS8625: 无法将 null 字面量转换为非 null  的引用类型。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(277,28): warning CS8622: “void ShapeStyleReplacementRuleForm.BtnOK_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(278,41): warning CS8622: “void ShapeStyleReplacementRuleForm.BtnSourceFillColor_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(279,41): warning CS8622: “void ShapeStyleReplacementRuleForm.BtnTargetFillColor_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(280,43): warning CS8622: “void ShapeStyleReplacementRuleForm.BtnSourceBorderColor_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托 “EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(281,43): warning CS8622: “void ShapeStyleReplacementRuleForm.BtnTargetBorderColor_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托 “EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(196,38): warning CS8622: “void ImageReplacementRuleForm.BtnBrowseSource_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(197,38): warning CS8622: “void ImageReplacementRuleForm.BtnBrowseTarget_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(198,28): warning CS8622: “void ImageReplacementRuleForm.BtnOK_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可 能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\FontNameReplacementRuleForm.cs(222,28): warning CS8622: “void FontNameReplacementRuleForm.BtnOK_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(328,28): warning CS8622: “void FontStyleReplacementRuleForm.BtnOK_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(240,28): warning CS8622: “void TextBoxReplacementRuleForm.BtnOK_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(241,50): warning CS8622: “void TextBoxReplacementRuleForm.ChkMatchByPosition_CheckedChanged(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。

在 18.0 中生成 成功，出现 19 警告