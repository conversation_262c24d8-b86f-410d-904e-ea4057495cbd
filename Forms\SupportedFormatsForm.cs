using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 支持格式设置窗体
    /// </summary>
    public partial class SupportedFormatsForm : Form
    {
        private readonly List<FormatInfo> _formats;
        private readonly Dictionary<string, CheckBox> _formatCheckBoxes;

        public SupportedFormatsForm()
        {
            InitializeComponent();
            _formats = GetSupportedFormats();
            _formatCheckBoxes = new Dictionary<string, CheckBox>();
            InitializeFormatControls();
            LoadCurrentSettings();
            ApplyModernStyle();
        }

        /// <summary>
        /// 获取支持的格式列表
        /// </summary>
        private List<FormatInfo> GetSupportedFormats()
        {
            return new List<FormatInfo>
            {
                new FormatInfo { Extension = ".ppt", Description = "PowerPoint 97-2003 演示文稿", IsDefault = true },
                new FormatInfo { Extension = ".pptx", Description = "PowerPoint 演示文稿", IsDefault = true },
                new FormatInfo { Extension = ".pptm", Description = "PowerPoint 启用宏的演示文稿", IsDefault = true },
                new FormatInfo { Extension = ".ppsx", Description = "PowerPoint 幻灯片放映", IsDefault = false },
                new FormatInfo { Extension = ".ppsm", Description = "PowerPoint 启用宏的幻灯片放映", IsDefault = false },
                new FormatInfo { Extension = ".potx", Description = "PowerPoint 模板", IsDefault = false },
                new FormatInfo { Extension = ".potm", Description = "PowerPoint 启用宏的模板", IsDefault = false },
                new FormatInfo { Extension = ".odp", Description = "OpenDocument 演示文稿", IsDefault = false },
                new FormatInfo { Extension = ".otp", Description = "OpenDocument 演示文稿模板", IsDefault = false }
            };
        }

        /// <summary>
        /// 初始化格式控件
        /// </summary>
        private void InitializeFormatControls()
        {
            int yPosition = 15;
            const int itemHeight = 35; // 增加行高
            const int leftMargin = 20;

            foreach (var format in _formats)
            {
                var checkBox = new CheckBox
                {
                    Name = $"chk{format.Extension.Replace(".", "")}",
                    Text = $"{format.Extension.ToUpper()} - {format.Description}",
                    Location = new Point(leftMargin, yPosition),
                    Size = new Size(420, 25), // 增加高度和宽度
                    Checked = format.IsDefault,
                    Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular),
                    ForeColor = Color.FromArgb(64, 64, 64),
                    AutoSize = false, // 禁用自动大小
                    TextAlign = ContentAlignment.MiddleLeft // 文字垂直居中
                };

                panelFormats.Controls.Add(checkBox);
                _formatCheckBoxes[format.Extension] = checkBox;
                yPosition += itemHeight;
            }

            // 不需要调整面板高度，让它使用设计器中设置的固定高度
        }

        /// <summary>
        /// 加载当前设置
        /// </summary>
        private void LoadCurrentSettings()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();
                var supportedFormats = config.ProcessSettings.SupportedFormats;

                foreach (var kvp in _formatCheckBoxes)
                {
                    kvp.Value.Checked = supportedFormats.Contains(kvp.Key);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        private void SaveSettings()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();
                config.ProcessSettings.SupportedFormats.Clear();

                foreach (var kvp in _formatCheckBoxes)
                {
                    if (kvp.Value.Checked)
                    {
                        config.ProcessSettings.SupportedFormats.Add(kvp.Key);
                    }
                }

                ConfigService.Instance.UpdateConfig(config);
                MessageBox.Show("设置保存成功！", "提示",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 应用现代化样式
        /// </summary>
        private void ApplyModernStyle()
        {
            // 窗体样式
            this.BackColor = Color.FromArgb(248, 249, 250);
            this.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular);

            // 面板样式
            panelFormats.BackColor = Color.White;
            panelFormats.BorderStyle = BorderStyle.FixedSingle;

            // 按钮样式
            btnOK.BackColor = Color.FromArgb(0, 123, 255);
            btnOK.ForeColor = Color.White;
            btnOK.FlatStyle = FlatStyle.Flat;
            btnOK.FlatAppearance.BorderSize = 0;

            btnCancel.BackColor = Color.FromArgb(108, 117, 125);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.FlatAppearance.BorderSize = 0;

            btnSelectAll.BackColor = Color.FromArgb(40, 167, 69);
            btnSelectAll.ForeColor = Color.White;
            btnSelectAll.FlatStyle = FlatStyle.Flat;
            btnSelectAll.FlatAppearance.BorderSize = 0;

            btnDeselectAll.BackColor = Color.FromArgb(220, 53, 69);
            btnDeselectAll.ForeColor = Color.White;
            btnDeselectAll.FlatStyle = FlatStyle.Flat;
            btnDeselectAll.FlatAppearance.BorderSize = 0;
        }

        /// <summary>
        /// 全选按钮点击事件
        /// </summary>
        private void BtnSelectAll_Click(object sender, EventArgs e)
        {
            foreach (var checkBox in _formatCheckBoxes.Values)
            {
                checkBox.Checked = true;
            }
        }

        /// <summary>
        /// 取消全选按钮点击事件
        /// </summary>
        private void BtnDeselectAll_Click(object sender, EventArgs e)
        {
            foreach (var checkBox in _formatCheckBoxes.Values)
            {
                checkBox.Checked = false;
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object sender, EventArgs e)
        {
            // 检查是否至少选择了一种格式
            if (!_formatCheckBoxes.Values.Any(cb => cb.Checked))
            {
                MessageBox.Show("请至少选择一种支持的文件格式！", "提示",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            SaveSettings();
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }

    /// <summary>
    /// 格式信息类
    /// </summary>
    public class FormatInfo
    {
        public string Extension { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsDefault { get; set; } = false;
    }
}
