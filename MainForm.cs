using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli
{
    public partial class MainForm : Form
    {
        // 功能按钮数组
        private CheckBox[] functionCheckBoxes = null!;
        private Button[] functionButtons = null!;

        // 操作按钮数组
        private Button[] actionButtons = null!;

        // 功能名称
        private readonly string[] functionNames = {
            "页面设置", "内容删除设置", "内容替换设置",
            "PPT格式设置", "匹配段落格式", "页眉页脚设置",
            "文档属性", "文件名替换", "PPT格式转换"
        };

        // 操作按钮名称
        private readonly string[] actionNames = {
            "开始处理", "定时处理", "停止处理",
            "日志设置", "清空日志", "导出配置",
            "导入配置", "打开源目录", "打开输出目录"
        };

        public MainForm()
        {
            InitializeComponent();
            InitializeCustomControls();
            LoadConfiguration();
        }

        /// <summary>
        /// 初始化自定义控件
        /// </summary>
        private void InitializeCustomControls()
        {
            InitializeFunctionButtons();
            InitializeActionButtons();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化功能按钮
        /// </summary>
        private void InitializeFunctionButtons()
        {
            functionCheckBoxes = new CheckBox[9];
            functionButtons = new Button[9];

            for (int i = 0; i < 9; i++)
            {
                int row = i / 3;
                int col = i % 3;

                // 创建复选框
                var checkBox = new CheckBox
                {
                    Name = $"chkFunction{i}",
                    Text = "",
                    Size = new Size(20, 20),
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Checked = false
                };

                // 创建功能按钮
                var button = new Button
                {
                    Name = $"btnFunction{i}",
                    Text = functionNames[i],
                    Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom,
                    UseVisualStyleBackColor = true,
                    Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular),
                    FlatStyle = FlatStyle.Standard
                };

                // 创建面板容器，设置边距
                var panel = new Panel
                {
                    Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom,
                    Margin = new Padding(5, 3, 5, 3),
                    Padding = new Padding(5, 5, 5, 5)
                };

                // 添加控件到面板
                panel.Controls.Add(checkBox);
                panel.Controls.Add(button);

                // 定义Resize事件处理器
                EventHandler resizeHandler = (s, e) =>
                {
                    var panelHeight = panel.ClientSize.Height;
                    var panelWidth = panel.ClientSize.Width;

                    // 复选框垂直居中
                    var checkBoxY = (panelHeight - checkBox.Height) / 2;
                    checkBox.Location = new Point(5, checkBoxY);

                    // 按钮位置和大小
                    button.Location = new Point(30, 5);
                    button.Size = new Size(panelWidth - 40, panelHeight - 10);
                };

                // 设置面板的Resize事件
                panel.Resize += resizeHandler;

                tableLayoutPanelFunctions.Controls.Add(panel, col, row);

                functionCheckBoxes[i] = checkBox;
                functionButtons[i] = button;

                // 绑定事件
                int index = i; // 闭包变量
                button.Click += (s, e) => OnFunctionButtonClick(index);

                // 延迟触发Resize事件以确保初始布局正确
                var initTimer = new System.Windows.Forms.Timer();
                initTimer.Interval = 50;
                initTimer.Tick += (s, e) =>
                {
                    initTimer.Stop();
                    initTimer.Dispose();
                    // 手动调用Resize事件处理器来设置初始位置
                    resizeHandler(panel, EventArgs.Empty);
                };
                initTimer.Start();
            }
        }

        /// <summary>
        /// 初始化操作按钮
        /// </summary>
        private void InitializeActionButtons()
        {
            actionButtons = new Button[9];

            for (int i = 0; i < 9; i++)
            {
                int row = i / 3;
                int col = i % 3;

                var button = new Button
                {
                    Name = $"btnAction{i}",
                    Text = actionNames[i],
                    Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom,
                    UseVisualStyleBackColor = true,
                    Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular),
                    FlatStyle = FlatStyle.Standard,
                    Margin = new Padding(8, 5, 8, 5)
                };

                tableLayoutPanelActions.Controls.Add(button, col, row);
                actionButtons[i] = button;

                // 绑定事件
                int index = i; // 闭包变量
                button.Click += (s, e) => OnActionButtonClick(index);
            }

            // 设置停止处理按钮初始状态为禁用
            actionButtons[2].Enabled = false; // 停止处理
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 路径浏览按钮
            btnBrowseSource.Click += BtnBrowseSource_Click;
            btnBrowseOutput.Click += BtnBrowseOutput_Click;

            // 全选/取消全选按钮
            btnSelectAll.Click += BtnSelectAll_Click;
            btnDeselectAll.Click += BtnDeselectAll_Click;

            // 支持格式设定按钮
            btnSupportedFormats.Click += BtnSupportedFormats_Click;

            // 设置拖放功能
            SetupDragDropForPaths();

            // 路径文本框变更事件
            txtSourcePath.TextChanged += TxtSourcePath_TextChanged;
            txtOutputPath.TextChanged += TxtOutputPath_TextChanged;

            // 设置处理设置区域的事件处理
            SetupProcessingSettingsEvents();

            // 设置NumericUpDown控件的文字居中对齐
            SetupNumericUpDownAlignment();

            // 窗体关闭事件
            this.FormClosing += MainForm_FormClosing;
        }

        /// <summary>
        /// 功能按钮点击事件
        /// </summary>
        private void OnFunctionButtonClick(int index)
        {
            try
            {
                switch (index)
                {
                    case 0: // 页面设置
                        OpenPageSetupForm();
                        break;
                    case 1: // 内容删除设置
                        OpenContentDeletionForm();
                        break;
                    case 2: // 内容替换设置
                        OpenContentReplacementForm();
                        break;
                    case 3: // PPT格式设置
                        MessageBox.Show("PPT格式设置功能待实现", "功能设置",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        break;
                    case 4: // 匹配段落格式
                        MessageBox.Show("匹配段落格式功能待实现", "功能设置",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        break;
                    case 5: // 页眉页脚设置
                        MessageBox.Show("页眉页脚设置功能待实现", "功能设置",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        break;
                    case 6: // 文档属性
                        MessageBox.Show("文档属性功能待实现", "功能设置",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        break;
                    case 7: // 文件名替换
                        MessageBox.Show("文件名替换功能待实现", "功能设置",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        break;
                    case 8: // PPT格式转换
                        MessageBox.Show("PPT格式转换功能待实现", "功能设置",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        break;
                    default:
                        MessageBox.Show($"未知功能: {functionNames[index]}", "错误",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开{functionNames[index]}设置窗口时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开页面设置窗体
        /// </summary>
        private void OpenPageSetupForm()
        {
            try
            {
                using var pageSetupForm = new Forms.PageSetupForm();
                var result = pageSetupForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    var settings = pageSetupForm.GetCurrentSettings();
                    ShowPathStatusMessage($"页面设置已更新: {settings.GetSizeDescription()}", false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开页面设置窗体时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开内容删除设置窗体
        /// </summary>
        private void OpenContentDeletionForm()
        {
            try
            {
                using var contentDeletionForm = new Forms.ContentDeletionForm();
                var result = contentDeletionForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    var settings = contentDeletionForm.GetCurrentSettings();
                    ShowPathStatusMessage("内容删除设置已更新", false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开内容删除设置窗体时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开内容替换设置窗体
        /// </summary>
        private void OpenContentReplacementForm()
        {
            try
            {
                using var contentReplacementForm = new Forms.ContentReplacementForm();
                var result = contentReplacementForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    ShowPathStatusMessage("内容替换设置已更新", false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开内容替换设置窗体时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 操作按钮点击事件
        /// </summary>
        private void OnActionButtonClick(int index)
        {
            switch (index)
            {
                case 0: // 开始处理
                    StartProcessing();
                    break;
                case 1: // 定时处理
                    ShowScheduleDialog();
                    break;
                case 2: // 停止处理
                    StopProcessing();
                    break;
                case 3: // 日志设置
                    ShowLogSettings();
                    break;
                case 4: // 清空日志
                    ClearLogs();
                    break;
                case 5: // 导出配置
                    ExportConfiguration();
                    break;
                case 6: // 导入配置
                    ImportConfiguration();
                    break;
                case 7: // 打开源目录
                    OpenSourceDirectory();
                    break;
                case 8: // 打开输出目录
                    OpenOutputDirectory();
                    break;
            }
        }

        /// <summary>
        /// 浏览源目录
        /// </summary>
        private void BtnBrowseSource_Click(object? sender, EventArgs e)
        {
            using var dialog = new FolderBrowserDialog();
            dialog.Description = "选择源目录";
            dialog.UseDescriptionForTitle = true;

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                txtSourcePath.Text = dialog.SelectedPath;
            }
        }

        /// <summary>
        /// 浏览输出目录
        /// </summary>
        private void BtnBrowseOutput_Click(object? sender, EventArgs e)
        {
            using var dialog = new FolderBrowserDialog();
            dialog.Description = "选择输出目录";
            dialog.UseDescriptionForTitle = true;

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                txtOutputPath.Text = dialog.SelectedPath;
            }
        }

        /// <summary>
        /// 全选功能
        /// </summary>
        private void BtnSelectAll_Click(object? sender, EventArgs e)
        {
            foreach (var checkBox in functionCheckBoxes)
            {
                checkBox.Checked = true;
            }
        }

        /// <summary>
        /// 取消全选功能
        /// </summary>
        private void BtnDeselectAll_Click(object? sender, EventArgs e)
        {
            foreach (var checkBox in functionCheckBoxes)
            {
                checkBox.Checked = false;
            }
        }

        /// <summary>
        /// 支持格式设定按钮点击事件
        /// </summary>
        private void BtnSupportedFormats_Click(object? sender, EventArgs e)
        {
            try
            {
                using var formatsForm = new Forms.SupportedFormatsForm();
                var result = formatsForm.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    // 格式设置已保存，可以在这里添加其他处理逻辑
                    ShowPathStatusMessage("支持格式设置已更新", false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开支持格式设定窗体时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置处理设置区域的事件处理
        /// </summary>
        private void SetupProcessingSettingsEvents()
        {
            // 线程数变更事件
            numThreadCount.ValueChanged += NumThreadCount_ValueChanged;

            // 重试次数变更事件
            numRetryCount.ValueChanged += NumRetryCount_ValueChanged;

            // 批处理数量变更事件
            numBatchSize.ValueChanged += NumBatchSize_ValueChanged;

            // 冲突处理选项变更事件
            radioCopy.CheckedChanged += RadioCopy_CheckedChanged;
            radioMove.CheckedChanged += RadioMove_CheckedChanged;
            chkDeleteSource.CheckedChanged += ChkDeleteSource_CheckedChanged;

            // 路径选项变更事件
            chkIncludeSubfolders.CheckedChanged += ChkIncludeSubfolders_CheckedChanged;
            chkKeepStructure.CheckedChanged += ChkKeepStructure_CheckedChanged;
        }

        /// <summary>
        /// 设置NumericUpDown控件的文字居中对齐
        /// </summary>
        private void SetupNumericUpDownAlignment()
        {
            // 延迟设置，确保控件已完全初始化
            this.Load += (s, e) =>
            {
                // 设置线程数输入框文字居中
                SetNumericUpDownTextAlign(numThreadCount);

                // 设置重试次数输入框文字居中
                SetNumericUpDownTextAlign(numRetryCount);

                // 设置批处理数量输入框文字居中
                SetNumericUpDownTextAlign(numBatchSize);
            };
        }

        /// <summary>
        /// 设置NumericUpDown控件的文字居中对齐
        /// </summary>
        private void SetNumericUpDownTextAlign(NumericUpDown numericUpDown)
        {
            try
            {
                // 方法1：通过反射获取内部TextBox控件
                var textBoxField = typeof(NumericUpDown).GetField("upDownEdit",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (textBoxField?.GetValue(numericUpDown) is TextBox textBox)
                {
                    textBox.TextAlign = HorizontalAlignment.Center;
                    return;
                }

                // 方法2：遍历子控件查找TextBox
                foreach (Control control in numericUpDown.Controls)
                {
                    if (control is TextBox tb)
                    {
                        tb.TextAlign = HorizontalAlignment.Center;
                        return;
                    }
                }

                // 方法3：使用Timer延迟设置
                var timer = new System.Windows.Forms.Timer();
                timer.Interval = 100;
                timer.Tick += (s, e) =>
                {
                    timer.Stop();
                    timer.Dispose();

                    foreach (Control control in numericUpDown.Controls)
                    {
                        if (control is TextBox tb2)
                        {
                            tb2.TextAlign = HorizontalAlignment.Center;
                            break;
                        }
                    }
                };
                timer.Start();
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置数值框文字居中时出错: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 设置路径文本框的拖放功能
        /// </summary>
        private void SetupDragDropForPaths()
        {
            // 启用源路径文本框的拖放功能
            txtSourcePath.AllowDrop = true;
            txtSourcePath.DragEnter += TxtSourcePath_DragEnter;
            txtSourcePath.DragDrop += TxtSourcePath_DragDrop;
            txtSourcePath.DragOver += TxtPath_DragOver;
            txtSourcePath.DragLeave += TxtPath_DragLeave;

            // 启用输出路径文本框的拖放功能
            txtOutputPath.AllowDrop = true;
            txtOutputPath.DragEnter += TxtOutputPath_DragEnter;
            txtOutputPath.DragDrop += TxtOutputPath_DragDrop;
            txtOutputPath.DragOver += TxtPath_DragOver;
            txtOutputPath.DragLeave += TxtPath_DragLeave;
        }

        /// <summary>
        /// 源路径拖拽进入事件
        /// </summary>
        private void TxtSourcePath_DragEnter(object? sender, DragEventArgs e)
        {
            HandleDragEnter(e);
        }

        /// <summary>
        /// 输出路径拖拽进入事件
        /// </summary>
        private void TxtOutputPath_DragEnter(object? sender, DragEventArgs e)
        {
            HandleDragEnter(e);
        }

        /// <summary>
        /// 拖拽悬停事件处理
        /// </summary>
        private void TxtPath_DragOver(object? sender, DragEventArgs e)
        {
            // 检查是否为文件夹拖拽
            if (e.Data?.GetDataPresent(DataFormats.FileDrop) == true)
            {
                var files = (string[]?)e.Data.GetData(DataFormats.FileDrop);
                if (files != null && files.Length > 0 && Directory.Exists(files[0]))
                {
                    e.Effect = DragDropEffects.Copy;

                    // 改变文本框背景色以提供视觉反馈
                    if (sender is TextBox textBox)
                    {
                        textBox.BackColor = Color.LightBlue;
                    }
                }
                else
                {
                    e.Effect = DragDropEffects.None;
                }
            }
            else
            {
                e.Effect = DragDropEffects.None;
            }
        }

        /// <summary>
        /// 拖拽离开事件处理
        /// </summary>
        private void TxtPath_DragLeave(object? sender, EventArgs e)
        {
            // 恢复文本框背景色
            if (sender is TextBox textBox)
            {
                textBox.BackColor = SystemColors.Window;
            }
        }

        /// <summary>
        /// 处理拖拽进入事件
        /// </summary>
        private void HandleDragEnter(DragEventArgs e)
        {
            // 检查拖拽的数据是否为文件
            if (e.Data?.GetDataPresent(DataFormats.FileDrop) == true)
            {
                var files = (string[]?)e.Data.GetData(DataFormats.FileDrop);
                if (files != null && files.Length > 0)
                {
                    // 只允许拖拽文件夹
                    if (Directory.Exists(files[0]))
                    {
                        e.Effect = DragDropEffects.Copy;
                    }
                    else
                    {
                        e.Effect = DragDropEffects.None;
                    }
                }
                else
                {
                    e.Effect = DragDropEffects.None;
                }
            }
            else
            {
                e.Effect = DragDropEffects.None;
            }
        }

        /// <summary>
        /// 源路径拖放完成事件
        /// </summary>
        private void TxtSourcePath_DragDrop(object? sender, DragEventArgs e)
        {
            HandleDragDrop(sender, e, "源目录");
        }

        /// <summary>
        /// 输出路径拖放完成事件
        /// </summary>
        private void TxtOutputPath_DragDrop(object? sender, DragEventArgs e)
        {
            HandleDragDrop(sender, e, "输出目录");
        }

        /// <summary>
        /// 处理拖放完成事件
        /// </summary>
        private void HandleDragDrop(object? sender, DragEventArgs e, string pathType)
        {
            try
            {
                if (sender is TextBox textBox)
                {
                    // 恢复文本框背景色
                    textBox.BackColor = SystemColors.Window;

                    if (e.Data?.GetDataPresent(DataFormats.FileDrop) == true)
                    {
                        var files = (string[]?)e.Data.GetData(DataFormats.FileDrop);
                        if (files != null && files.Length > 0)
                        {
                            var folderPath = files[0];
                            if (Directory.Exists(folderPath))
                            {
                                // 验证路径有效性
                                if (ValidatePath(folderPath, pathType))
                                {
                                    textBox.Text = folderPath;

                                    // 显示成功提示
                                    ShowPathStatusMessage($"{pathType}设置成功: {folderPath}", false);
                                }
                            }
                            else
                            {
                                ShowPathStatusMessage($"拖放的不是有效的文件夹: {folderPath}", true);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置{pathType}时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 源路径文本变更事件
        /// </summary>
        private void TxtSourcePath_TextChanged(object? sender, EventArgs e)
        {
            ValidateAndUpdatePathStatus(txtSourcePath.Text, "源目录");
        }

        /// <summary>
        /// 输出路径文本变更事件
        /// </summary>
        private void TxtOutputPath_TextChanged(object? sender, EventArgs e)
        {
            ValidateAndUpdatePathStatus(txtOutputPath.Text, "输出目录");
        }

        /// <summary>
        /// 验证并更新路径状态
        /// </summary>
        private void ValidateAndUpdatePathStatus(string path, string pathType)
        {
            if (string.IsNullOrWhiteSpace(path))
            {
                return; // 空路径不显示错误
            }

            if (!ValidatePath(path, pathType))
            {
                // 路径无效时的处理已在ValidatePath中完成
                return;
            }

            // 路径有效，可以进行其他处理
            // 例如：自动保存配置、更新UI状态等
        }

        /// <summary>
        /// 验证路径有效性
        /// </summary>
        private bool ValidatePath(string path, string pathType)
        {
            try
            {
                // 检查路径格式是否有效
                if (!Path.IsPathRooted(path))
                {
                    ShowPathStatusMessage($"{pathType}必须是绝对路径", true);
                    return false;
                }

                // 检查路径是否存在
                if (!Directory.Exists(path))
                {
                    ShowPathStatusMessage($"{pathType}不存在: {path}", true);
                    return false;
                }

                // 检查路径访问权限
                try
                {
                    var testPath = Path.Combine(path, "test_access_" + Guid.NewGuid().ToString("N")[..8]);
                    Directory.CreateDirectory(testPath);
                    Directory.Delete(testPath);
                }
                catch (UnauthorizedAccessException)
                {
                    ShowPathStatusMessage($"没有访问{pathType}的权限: {path}", true);
                    return false;
                }
                catch (Exception ex)
                {
                    ShowPathStatusMessage($"无法访问{pathType}: {ex.Message}", true);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"验证{pathType}时发生错误: {ex.Message}", true);
                return false;
            }
        }

        /// <summary>
        /// 显示路径状态消息
        /// </summary>
        private void ShowPathStatusMessage(string message, bool isError)
        {
            // 在状态栏或其他位置显示消息
            // 这里暂时使用控制台输出，后续可以改为状态栏显示
            if (isError)
            {
                Console.WriteLine($"[错误] {message}");
                // 可以考虑在UI上显示错误状态
            }
            else
            {
                Console.WriteLine($"[信息] {message}");
                // 可以考虑在UI上显示成功状态
            }
        }

        /// <summary>
        /// 线程数变更事件
        /// </summary>
        private void NumThreadCount_ValueChanged(object? sender, EventArgs e)
        {
            try
            {
                // 验证线程数范围
                if (numThreadCount.Value < 1 || numThreadCount.Value > 16)
                {
                    ShowPathStatusMessage($"线程数应在1-16之间，当前值: {numThreadCount.Value}", true);
                    return;
                }

                // 自动保存配置
                SaveProcessingSettings();
                ShowPathStatusMessage($"线程数已设置为: {numThreadCount.Value}", false);
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置线程数时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 重试次数变更事件
        /// </summary>
        private void NumRetryCount_ValueChanged(object? sender, EventArgs e)
        {
            try
            {
                // 验证重试次数范围
                if (numRetryCount.Value < 0 || numRetryCount.Value > 10)
                {
                    ShowPathStatusMessage($"重试次数应在0-10之间，当前值: {numRetryCount.Value}", true);
                    return;
                }

                // 自动保存配置
                SaveProcessingSettings();
                ShowPathStatusMessage($"重试次数已设置为: {numRetryCount.Value}", false);
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置重试次数时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 批处理数量变更事件
        /// </summary>
        private void NumBatchSize_ValueChanged(object? sender, EventArgs e)
        {
            try
            {
                // 验证批处理数量范围
                if (numBatchSize.Value < 1 || numBatchSize.Value > 1000)
                {
                    ShowPathStatusMessage($"批处理数量应在1-1000之间，当前值: {numBatchSize.Value}", true);
                    return;
                }

                // 自动保存配置
                SaveProcessingSettings();
                ShowPathStatusMessage($"批处理数量已设置为: {numBatchSize.Value}", false);
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置批处理数量时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 复制文件选项变更事件
        /// </summary>
        private void RadioCopy_CheckedChanged(object? sender, EventArgs e)
        {
            if (radioCopy.Checked)
            {
                try
                {
                    SaveProcessingSettings();
                    ShowPathStatusMessage("已设置为复制文件模式", false);
                }
                catch (Exception ex)
                {
                    ShowPathStatusMessage($"设置复制模式时发生错误: {ex.Message}", true);
                }
            }
        }

        /// <summary>
        /// 移动文件选项变更事件
        /// </summary>
        private void RadioMove_CheckedChanged(object? sender, EventArgs e)
        {
            if (radioMove.Checked)
            {
                try
                {
                    SaveProcessingSettings();
                    ShowPathStatusMessage("已设置为移动文件模式", false);
                }
                catch (Exception ex)
                {
                    ShowPathStatusMessage($"设置移动模式时发生错误: {ex.Message}", true);
                }
            }
        }

        /// <summary>
        /// 直接处理源文件选项变更事件
        /// </summary>
        private void ChkDeleteSource_CheckedChanged(object? sender, EventArgs e)
        {
            try
            {
                SaveProcessingSettings();
                var mode = chkDeleteSource.Checked ? "直接处理源文件" : "处理副本文件";
                ShowPathStatusMessage($"已设置为: {mode}", false);
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置处理模式时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 包含子目录选项变更事件
        /// </summary>
        private void ChkIncludeSubfolders_CheckedChanged(object? sender, EventArgs e)
        {
            try
            {
                SaveProcessingSettings();
                var mode = chkIncludeSubfolders.Checked ? "包含子目录" : "仅处理当前目录";
                ShowPathStatusMessage($"已设置为: {mode}", false);
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置子目录处理模式时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 保持目录结构选项变更事件
        /// </summary>
        private void ChkKeepStructure_CheckedChanged(object? sender, EventArgs e)
        {
            try
            {
                SaveProcessingSettings();
                var mode = chkKeepStructure.Checked ? "保持原目录结构" : "平铺到输出目录";
                ShowPathStatusMessage($"已设置为: {mode}", false);
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"设置目录结构模式时发生错误: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 保存处理设置（仅保存处理相关设置，不保存窗体设置）
        /// </summary>
        private void SaveProcessingSettings()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();

                // 保存路径设置
                config.PathSettings.SourcePath = txtSourcePath.Text;
                config.PathSettings.OutputPath = txtOutputPath.Text;
                config.PathSettings.IncludeSubfolders = chkIncludeSubfolders.Checked;
                config.PathSettings.KeepDirectoryStructure = chkKeepStructure.Checked;

                // 保存处理设置
                config.ProcessSettings.CopyFiles = radioCopy.Checked;
                config.ProcessSettings.ProcessSourceDirectly = chkDeleteSource.Checked;
                config.ProcessSettings.ThreadCount = (int)numThreadCount.Value;
                config.ProcessSettings.RetryCount = (int)numRetryCount.Value;
                config.ProcessSettings.BatchSize = (int)numBatchSize.Value;

                ConfigService.Instance.UpdateConfig(config);
            }
            catch (Exception ex)
            {
                ShowPathStatusMessage($"保存处理设置失败: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 开始处理
        /// </summary>
        private void StartProcessing()
        {
            if (string.IsNullOrWhiteSpace(txtSourcePath.Text))
            {
                MessageBox.Show("请选择源目录", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(txtOutputPath.Text))
            {
                MessageBox.Show("请选择输出目录", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 验证路径有效性
            if (!ValidatePath(txtSourcePath.Text, "源目录") ||
                !ValidatePath(txtOutputPath.Text, "输出目录"))
            {
                MessageBox.Show("请检查路径设置是否正确", "路径验证失败",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // TODO: 实现处理逻辑
            MessageBox.Show("开始处理功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 显示定时处理对话框
        /// </summary>
        private void ShowScheduleDialog()
        {
            MessageBox.Show("定时处理功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 停止处理
        /// </summary>
        private void StopProcessing()
        {
            MessageBox.Show("停止处理功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 显示日志设置
        /// </summary>
        private void ShowLogSettings()
        {
            MessageBox.Show("日志设置功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 清空日志
        /// </summary>
        private void ClearLogs()
        {
            var result = MessageBox.Show("确定要清空所有日志文件吗？", "确认",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // TODO: 实现清空日志逻辑
                MessageBox.Show("日志已清空", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        /// <summary>
        /// 导出配置
        /// </summary>
        private void ExportConfiguration()
        {
            using var dialog = new FolderBrowserDialog();
            dialog.Description = "选择配置导出目录";
            dialog.UseDescriptionForTitle = true;

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                // 先保存当前配置
                SaveConfiguration();

                if (ConfigService.Instance.ExportConfig(dialog.SelectedPath))
                {
                    MessageBox.Show("配置导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("配置导出失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 导入配置
        /// </summary>
        private void ImportConfiguration()
        {
            using var dialog = new FolderBrowserDialog();
            dialog.Description = "选择配置导入目录";
            dialog.UseDescriptionForTitle = true;

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                var result = MessageBox.Show("导入配置将覆盖当前设置，是否继续？", "确认",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    if (ConfigService.Instance.ImportConfig(dialog.SelectedPath))
                    {
                        MessageBox.Show("配置导入成功！重新加载界面设置...", "提示",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadConfiguration();
                    }
                    else
                    {
                        MessageBox.Show("配置导入失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        /// <summary>
        /// 打开源目录
        /// </summary>
        private void OpenSourceDirectory()
        {
            if (!string.IsNullOrWhiteSpace(txtSourcePath.Text) && Directory.Exists(txtSourcePath.Text))
            {
                System.Diagnostics.Process.Start("explorer.exe", txtSourcePath.Text);
            }
            else
            {
                MessageBox.Show("源目录不存在", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 打开输出目录
        /// </summary>
        private void OpenOutputDirectory()
        {
            if (!string.IsNullOrWhiteSpace(txtOutputPath.Text) && Directory.Exists(txtOutputPath.Text))
            {
                System.Diagnostics.Process.Start("explorer.exe", txtOutputPath.Text);
            }
            else
            {
                MessageBox.Show("输出目录不存在", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();

                // 加载路径设置
                txtSourcePath.Text = config.PathSettings.SourcePath;
                txtOutputPath.Text = config.PathSettings.OutputPath;
                chkIncludeSubfolders.Checked = config.PathSettings.IncludeSubfolders;
                chkKeepStructure.Checked = config.PathSettings.KeepDirectoryStructure;

                // 加载处理设置
                radioCopy.Checked = config.ProcessSettings.CopyFiles;
                radioMove.Checked = !config.ProcessSettings.CopyFiles;
                chkDeleteSource.Checked = config.ProcessSettings.ProcessSourceDirectly;
                numThreadCount.Value = config.ProcessSettings.ThreadCount;
                numRetryCount.Value = config.ProcessSettings.RetryCount;
                numBatchSize.Value = config.ProcessSettings.BatchSize;

                // 加载功能启用状态
                for (int i = 0; i < functionNames.Length; i++)
                {
                    if (config.FunctionEnabled.ContainsKey(functionNames[i]))
                    {
                        functionCheckBoxes[i].Checked = config.FunctionEnabled[functionNames[i]];
                    }
                }

                // 加载窗体设置
                if (config.FormSettings.LocationX >= 0 && config.FormSettings.LocationY >= 0)
                {
                    this.StartPosition = FormStartPosition.Manual;
                    this.Location = new Point(config.FormSettings.LocationX, config.FormSettings.LocationY);
                }

                this.Size = new Size(config.FormSettings.Width, config.FormSettings.Height);
                this.WindowState = (FormWindowState)config.FormSettings.WindowState;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"配置加载失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        private void SaveConfiguration()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();

                // 保存路径设置
                config.PathSettings.SourcePath = txtSourcePath.Text;
                config.PathSettings.OutputPath = txtOutputPath.Text;
                config.PathSettings.IncludeSubfolders = chkIncludeSubfolders.Checked;
                config.PathSettings.KeepDirectoryStructure = chkKeepStructure.Checked;

                // 保存处理设置
                config.ProcessSettings.CopyFiles = radioCopy.Checked;
                config.ProcessSettings.ProcessSourceDirectly = chkDeleteSource.Checked;
                config.ProcessSettings.ThreadCount = (int)numThreadCount.Value;
                config.ProcessSettings.RetryCount = (int)numRetryCount.Value;
                config.ProcessSettings.BatchSize = (int)numBatchSize.Value;

                // 保存功能启用状态
                for (int i = 0; i < functionNames.Length; i++)
                {
                    config.FunctionEnabled[functionNames[i]] = functionCheckBoxes[i].Checked;
                }

                // 保存窗体设置
                if (this.WindowState == FormWindowState.Normal)
                {
                    config.FormSettings.LocationX = this.Location.X;
                    config.FormSettings.LocationY = this.Location.Y;
                    config.FormSettings.Width = this.Size.Width;
                    config.FormSettings.Height = this.Size.Height;
                }
                config.FormSettings.WindowState = (int)this.WindowState;

                ConfigService.Instance.UpdateConfig(config);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"配置保存失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void MainForm_FormClosing(object? sender, FormClosingEventArgs e)
        {
            SaveConfiguration();
        }
    }
}
