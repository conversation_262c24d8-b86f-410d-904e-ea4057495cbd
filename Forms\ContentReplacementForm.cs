using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 内容替换设置窗体
    /// </summary>
    public partial class ContentReplacementForm : Form
    {
        // 当前设置
        private ContentReplacementSettings _currentSettings = new();

        // 各标签页的控件容器
        private readonly Dictionary<string, Panel> _tabPanels = new();

        public ContentReplacementForm()
        {
            InitializeComponent();
            InitializeSettings();
            InitializeTabPages();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化设置
        /// </summary>
        private void InitializeSettings()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();
                _currentSettings = config.ContentReplacementSettings;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载内容替换设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                _currentSettings = new ContentReplacementSettings();
            }
        }

        /// <summary>
        /// 初始化标签页
        /// </summary>
        private void InitializeTabPages()
        {
            try
            {
                // 初始化文本替换标签页
                InitializeTextReplacementTab();

                // 初始化形状替换标签页
                InitializeShapeReplacementTab();

                // 初始化字体替换标签页
                InitializeFontReplacementTab();

                // 初始化颜色替换标签页
                InitializeColorReplacementTab();

                // 加载当前设置到界面
                LoadSettingsToUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化标签页失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化文本替换标签页
        /// </summary>
        private void InitializeTextReplacementTab()
        {
            var panel = CreateScrollablePanel();
            tabPageTextReplacement.Controls.Add(panel);
            _tabPanels["TextReplacement"] = panel;

            int yPos = 15;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用文本替换功能", 15, yPos);
            chkMasterSwitch.Name = "chkTextReplacementMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 40;

            // 文本替换类型选项
            var grpReplacementTypes = CreateGroupBox("文本替换类型", 15, yPos, 720, 140);

            var chkNormalText = CreateCheckBox("普通文本替换", 20, 30);
            chkNormalText.Name = "chkEnableNormalTextReplacement";

            var chkRegexText = CreateCheckBox("正则表达式替换", 20, 60);
            chkRegexText.Name = "chkEnableRegexReplacement";

            var chkBatchText = CreateCheckBox("批量文本替换", 20, 90);
            chkBatchText.Name = "chkEnableBatchTextReplacement";

            var chkRangeText = CreateCheckBox("指定范围替换", 400, 30);
            chkRangeText.Name = "chkEnableRangeReplacement";

            grpReplacementTypes.Controls.AddRange(new Control[] { chkNormalText, chkRegexText, chkBatchText, chkRangeText });
            panel.Controls.Add(grpReplacementTypes);
            yPos += 150;

            // 替换范围设置
            var grpReplacementRange = CreateGroupBox("替换范围设置", 15, yPos, 720, 140);

            var chkReplaceInTitles = CreateCheckBox("替换标题文本", 20, 30);
            chkReplaceInTitles.Name = "chkReplaceInTitles";

            var chkReplaceInContent = CreateCheckBox("替换内容文本", 20, 60);
            chkReplaceInContent.Name = "chkReplaceInContent";

            var chkReplaceInNotes = CreateCheckBox("替换备注文本", 20, 90);
            chkReplaceInNotes.Name = "chkReplaceInNotes";

            var chkReplaceInTables = CreateCheckBox("替换表格文本", 400, 30);
            chkReplaceInTables.Name = "chkReplaceInTables";

            var chkReplaceInCharts = CreateCheckBox("替换图表文本", 400, 60);
            chkReplaceInCharts.Name = "chkReplaceInCharts";

            grpReplacementRange.Controls.AddRange(new Control[] {
                chkReplaceInTitles, chkReplaceInContent, chkReplaceInNotes,
                chkReplaceInTables, chkReplaceInCharts
            });
            panel.Controls.Add(grpReplacementRange);
            yPos += 150;

            // 文本替换规则列表
            var grpReplacementRules = CreateGroupBox("文本替换规则", 15, yPos, 720, 320);

            var listViewRules = new ListView
            {
                Name = "listViewTextReplacementRules",
                Location = new Point(20, 30),
                Size = new Size(680, 220),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                CheckBoxes = true
            };

            listViewRules.Columns.Add("启用", 50);
            listViewRules.Columns.Add("规则名称", 120);
            listViewRules.Columns.Add("查找文本", 150);
            listViewRules.Columns.Add("替换文本", 150);
            listViewRules.Columns.Add("正则表达式", 80);
            listViewRules.Columns.Add("区分大小写", 80);
            listViewRules.Columns.Add("全词匹配", 80);

            var btnAddRule = CreateButton("添加规则", 20, 260, 80, 35);
            btnAddRule.Name = "btnAddTextReplacementRule";

            var btnEditRule = CreateButton("编辑规则", 110, 260, 80, 35);
            btnEditRule.Name = "btnEditTextReplacementRule";

            var btnDeleteRule = CreateButton("删除规则", 200, 260, 80, 35);
            btnDeleteRule.Name = "btnDeleteTextReplacementRule";

            var btnImportRules = CreateButton("导入Excel", 290, 260, 80, 35);
            btnImportRules.Name = "btnImportTextReplacementRules";

            var btnExportRules = CreateButton("导出规则", 380, 260, 80, 35);
            btnExportRules.Name = "btnExportTextReplacementRules";

            var btnDownloadTemplate = CreateButton("下载模板", 470, 260, 80, 35);
            btnDownloadTemplate.Name = "btnDownloadExcelTemplate";

            grpReplacementRules.Controls.AddRange(new Control[] {
                listViewRules, btnAddRule, btnEditRule, btnDeleteRule, btnImportRules, btnExportRules, btnDownloadTemplate
            });
            panel.Controls.Add(grpReplacementRules);
            yPos += 330;

            // 导入说明
            var grpImportInstructions = CreateGroupBox("导入说明", 15, yPos, 720, 120);

            var lblInstructions = new Label
            {
                Text = "Excel导入说明：\n" +
                       "1. 点击\"下载模板\"按钮下载Excel模板文件\n" +
                       "2. 在模板中填写替换规则，每行一条规则\n" +
                       "3. 保存Excel文件后，点击\"导入Excel\"按钮选择文件导入\n" +
                       "4. 支持.xlsx和.xls格式的Excel文件",
                Location = new Point(20, 25),
                Size = new Size(680, 85),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            grpImportInstructions.Controls.Add(lblInstructions);
            panel.Controls.Add(grpImportInstructions);
        }

        /// <summary>
        /// 初始化形状替换标签页
        /// </summary>
        private void InitializeShapeReplacementTab()
        {
            var panel = CreateScrollablePanel();
            tabPageShapeReplacement.Controls.Add(panel);
            _tabPanels["ShapeReplacement"] = panel;

            int yPos = 15;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用形状替换功能", 15, yPos);
            chkMasterSwitch.Name = "chkShapeReplacementMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 40;

            // 形状替换类型选项
            var grpShapeTypes = CreateGroupBox("形状替换类型", 15, yPos, 720, 120);

            var chkImageReplacement = CreateCheckBox("图片替换", 20, 30);
            chkImageReplacement.Name = "chkEnableImageReplacement";

            var chkTextBoxReplacement = CreateCheckBox("文本框替换", 20, 60);
            chkTextBoxReplacement.Name = "chkEnableTextBoxReplacement";

            var chkShapeStyleReplacement = CreateCheckBox("形状样式替换", 400, 30);
            chkShapeStyleReplacement.Name = "chkEnableShapeStyleReplacement";

            grpShapeTypes.Controls.AddRange(new Control[] { chkImageReplacement, chkTextBoxReplacement, chkShapeStyleReplacement });
            panel.Controls.Add(grpShapeTypes);
            yPos += 130;

            // 图片替换规则
            CreateShapeReplacementRulesSection(panel, ref yPos, "图片替换规则", "ImageReplacement");

            // 文本框替换规则
            CreateShapeReplacementRulesSection(panel, ref yPos, "文本框替换规则", "TextBoxReplacement");

            // 形状样式替换规则
            CreateShapeReplacementRulesSection(panel, ref yPos, "形状样式替换规则", "ShapeStyleReplacement");
        }

        /// <summary>
        /// 创建形状替换规则区域
        /// </summary>
        private void CreateShapeReplacementRulesSection(Panel parentPanel, ref int yPos, string title, string namePrefix)
        {
            var grpRules = CreateGroupBox(title, 15, yPos, 720, 280);

            var listViewRules = new ListView
            {
                Name = $"listView{namePrefix}Rules",
                Location = new Point(20, 30),
                Size = new Size(680, 180),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                CheckBoxes = true
            };

            // 根据不同类型设置不同的列
            if (namePrefix == "ImageReplacement")
            {
                listViewRules.Columns.Add("启用", 50);
                listViewRules.Columns.Add("规则名称", 120);
                listViewRules.Columns.Add("源图片", 200);
                listViewRules.Columns.Add("目标图片", 200);
                listViewRules.Columns.Add("匹配方式", 100);
            }
            else if (namePrefix == "TextBoxReplacement")
            {
                listViewRules.Columns.Add("启用", 50);
                listViewRules.Columns.Add("规则名称", 120);
                listViewRules.Columns.Add("源文本", 200);
                listViewRules.Columns.Add("目标文本", 200);
                listViewRules.Columns.Add("匹配方式", 100);
            }
            else if (namePrefix == "ShapeStyleReplacement")
            {
                listViewRules.Columns.Add("启用", 50);
                listViewRules.Columns.Add("规则名称", 120);
                listViewRules.Columns.Add("源形状类型", 150);
                listViewRules.Columns.Add("目标形状类型", 150);
                listViewRules.Columns.Add("替换内容", 150);
            }

            var btnAdd = CreateButton("添加", 20, 220, 70, 35);
            btnAdd.Name = $"btnAdd{namePrefix}Rule";

            var btnEdit = CreateButton("编辑", 100, 220, 70, 35);
            btnEdit.Name = $"btnEdit{namePrefix}Rule";

            var btnDelete = CreateButton("删除", 180, 220, 70, 35);
            btnDelete.Name = $"btnDelete{namePrefix}Rule";

            grpRules.Controls.AddRange(new Control[] { listViewRules, btnAdd, btnEdit, btnDelete });
            parentPanel.Controls.Add(grpRules);
            yPos += 290;
        }

        /// <summary>
        /// 初始化字体替换标签页
        /// </summary>
        private void InitializeFontReplacementTab()
        {
            var panel = CreateScrollablePanel();
            tabPageFontReplacement.Controls.Add(panel);
            _tabPanels["FontReplacement"] = panel;

            int yPos = 15;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用字体替换功能", 15, yPos);
            chkMasterSwitch.Name = "chkFontReplacementMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 40;

            // 字体替换类型选项
            var grpFontTypes = CreateGroupBox("字体替换类型", 15, yPos, 720, 120);

            var chkFontNameReplacement = CreateCheckBox("字体名称替换", 20, 30);
            chkFontNameReplacement.Name = "chkEnableFontNameReplacement";

            var chkFontStyleReplacement = CreateCheckBox("字体样式替换", 20, 60);
            chkFontStyleReplacement.Name = "chkEnableFontStyleReplacement";

            var chkFontEmbedding = CreateCheckBox("字体嵌入", 400, 30);
            chkFontEmbedding.Name = "chkEnableFontEmbedding";

            grpFontTypes.Controls.AddRange(new Control[] { chkFontNameReplacement, chkFontStyleReplacement, chkFontEmbedding });
            panel.Controls.Add(grpFontTypes);
            yPos += 130;

            // 字体名称替换规则
            CreateFontReplacementRulesSection(panel, ref yPos, "字体名称替换规则", "FontNameReplacement");

            // 字体样式替换规则
            CreateFontReplacementRulesSection(panel, ref yPos, "字体样式替换规则", "FontStyleReplacement");

            // 字体嵌入设置
            CreateFontEmbeddingSection(panel, ref yPos);
        }

        /// <summary>
        /// 创建字体替换规则区域
        /// </summary>
        private void CreateFontReplacementRulesSection(Panel parentPanel, ref int yPos, string title, string namePrefix)
        {
            var grpRules = CreateGroupBox(title, 15, yPos, 720, 280);

            var listViewRules = new ListView
            {
                Name = $"listView{namePrefix}Rules",
                Location = new Point(20, 30),
                Size = new Size(680, 180),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                CheckBoxes = true
            };

            if (namePrefix == "FontNameReplacement")
            {
                listViewRules.Columns.Add("启用", 50);
                listViewRules.Columns.Add("规则名称", 120);
                listViewRules.Columns.Add("源字体", 200);
                listViewRules.Columns.Add("目标字体", 200);
                listViewRules.Columns.Add("精确匹配", 80);
                listViewRules.Columns.Add("包含子字体", 80);
            }
            else if (namePrefix == "FontStyleReplacement")
            {
                listViewRules.Columns.Add("启用", 50);
                listViewRules.Columns.Add("规则名称", 120);
                listViewRules.Columns.Add("源字体", 150);
                listViewRules.Columns.Add("目标字体", 150);
                listViewRules.Columns.Add("替换内容", 200);
            }

            var btnAdd = CreateButton("添加", 20, 220, 70, 35);
            btnAdd.Name = $"btnAdd{namePrefix}Rule";

            var btnEdit = CreateButton("编辑", 100, 220, 70, 35);
            btnEdit.Name = $"btnEdit{namePrefix}Rule";

            var btnDelete = CreateButton("删除", 180, 220, 70, 35);
            btnDelete.Name = $"btnDelete{namePrefix}Rule";

            grpRules.Controls.AddRange(new Control[] { listViewRules, btnAdd, btnEdit, btnDelete });
            parentPanel.Controls.Add(grpRules);
            yPos += 290;
        }

        /// <summary>
        /// 创建字体嵌入设置区域
        /// </summary>
        private void CreateFontEmbeddingSection(Panel parentPanel, ref int yPos)
        {
            var grpEmbedding = CreateGroupBox("字体嵌入设置", 15, yPos, 720, 220);

            var chkEmbedAllFonts = CreateCheckBox("嵌入所有字体", 20, 30);
            chkEmbedAllFonts.Name = "chkEmbedAllFonts";

            var chkEmbedUsedCharactersOnly = CreateCheckBox("仅嵌入使用的字符", 20, 60);
            chkEmbedUsedCharactersOnly.Name = "chkEmbedUsedCharactersOnly";
            chkEmbedUsedCharactersOnly.Checked = true;

            var lblFontsToEmbed = CreateLabel("需要嵌入的字体:", 20, 95);
            var listBoxFontsToEmbed = new ListBox
            {
                Name = "listBoxFontsToEmbed",
                Location = new Point(20, 120),
                Size = new Size(320, 80),
                SelectionMode = SelectionMode.MultiExtended
            };

            var btnAddFont = CreateButton("添加字体", 350, 120, 90, 35);
            btnAddFont.Name = "btnAddFontToEmbed";

            var btnRemoveFont = CreateButton("移除字体", 350, 165, 90, 35);
            btnRemoveFont.Name = "btnRemoveFontToEmbed";

            grpEmbedding.Controls.AddRange(new Control[] {
                chkEmbedAllFonts, chkEmbedUsedCharactersOnly,
                lblFontsToEmbed, listBoxFontsToEmbed, btnAddFont, btnRemoveFont
            });
            parentPanel.Controls.Add(grpEmbedding);
            yPos += 230;
        }

        /// <summary>
        /// 初始化颜色替换标签页
        /// </summary>
        private void InitializeColorReplacementTab()
        {
            var panel = CreateScrollablePanel();
            tabPageColorReplacement.Controls.Add(panel);
            _tabPanels["ColorReplacement"] = panel;

            int yPos = 15;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用颜色替换功能", 15, yPos);
            chkMasterSwitch.Name = "chkColorReplacementMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 40;

            // 颜色替换类型选项
            var grpColorTypes = CreateGroupBox("颜色替换类型", 15, yPos, 720, 100);

            var chkThemeColorReplacement = CreateCheckBox("主题颜色替换", 20, 30);
            chkThemeColorReplacement.Name = "chkEnableThemeColorReplacement";

            var chkCustomColorReplacement = CreateCheckBox("自定义颜色替换", 400, 30);
            chkCustomColorReplacement.Name = "chkEnableCustomColorReplacement";

            grpColorTypes.Controls.AddRange(new Control[] { chkThemeColorReplacement, chkCustomColorReplacement });
            panel.Controls.Add(grpColorTypes);
            yPos += 110;

            // 主题颜色替换规则
            CreateColorReplacementRulesSection(panel, ref yPos, "主题颜色替换规则", "ThemeColorReplacement");

            // 自定义颜色替换规则
            CreateColorReplacementRulesSection(panel, ref yPos, "自定义颜色替换规则", "CustomColorReplacement");
        }

        /// <summary>
        /// 创建颜色替换规则区域
        /// </summary>
        private void CreateColorReplacementRulesSection(Panel parentPanel, ref int yPos, string title, string namePrefix)
        {
            var grpRules = CreateGroupBox(title, 15, yPos, 720, 280);

            var listViewRules = new ListView
            {
                Name = $"listView{namePrefix}Rules",
                Location = new Point(20, 30),
                Size = new Size(680, 180),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                CheckBoxes = true
            };

            if (namePrefix == "ThemeColorReplacement")
            {
                listViewRules.Columns.Add("启用", 50);
                listViewRules.Columns.Add("规则名称", 120);
                listViewRules.Columns.Add("源主题颜色", 150);
                listViewRules.Columns.Add("目标主题颜色", 150);
                listViewRules.Columns.Add("应用范围", 200);
            }
            else if (namePrefix == "CustomColorReplacement")
            {
                listViewRules.Columns.Add("启用", 50);
                listViewRules.Columns.Add("规则名称", 120);
                listViewRules.Columns.Add("源颜色", 120);
                listViewRules.Columns.Add("目标颜色", 120);
                listViewRules.Columns.Add("应用范围", 200);
            }

            var btnAdd = CreateButton("添加", 20, 220, 70, 35);
            btnAdd.Name = $"btnAdd{namePrefix}Rule";

            var btnEdit = CreateButton("编辑", 100, 220, 70, 35);
            btnEdit.Name = $"btnEdit{namePrefix}Rule";

            var btnDelete = CreateButton("删除", 180, 220, 70, 35);
            btnDelete.Name = $"btnDelete{namePrefix}Rule";

            grpRules.Controls.AddRange(new Control[] { listViewRules, btnAdd, btnEdit, btnDelete });
            parentPanel.Controls.Add(grpRules);
            yPos += 290;
        }
    }
}
