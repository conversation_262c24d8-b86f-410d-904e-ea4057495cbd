using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 非法词设置窗体
    /// </summary>
    public partial class IllegalWordsForm : Form
    {
        /// <summary>
        /// 非法词列表
        /// </summary>
        public List<string> IllegalWords { get; set; } = new List<string>();

        /// <summary>
        /// 窗体标题
        /// </summary>
        public string FormTitle
        {
            get => lblTitle.Text;
            set
            {
                lblTitle.Text = value;
                this.Text = value;
            }
        }

        /// <summary>
        /// 描述文本
        /// </summary>
        public string DescriptionText
        {
            get => lblDescription.Text;
            set => lblDescription.Text = value;
        }

        public IllegalWordsForm()
        {
            InitializeComponent();
            SetupEventHandlers();
        }

        /// <summary>
        /// 构造函数，带初始非法词列表
        /// </summary>
        /// <param name="initialWords">初始非法词列表</param>
        public IllegalWordsForm(List<string> initialWords) : this()
        {
            if (initialWords != null)
            {
                IllegalWords = new List<string>(initialWords);
                LoadWordsToUI();
            }
        }

        /// <summary>
        /// 构造函数，带标题和初始非法词列表
        /// </summary>
        /// <param name="title">窗体标题</param>
        /// <param name="initialWords">初始非法词列表</param>
        public IllegalWordsForm(string title, List<string> initialWords) : this(initialWords)
        {
            FormTitle = title;
        }

        /// <summary>
        /// 构造函数，带标题、描述和初始非法词列表
        /// </summary>
        /// <param name="title">窗体标题</param>
        /// <param name="description">描述文本</param>
        /// <param name="initialWords">初始非法词列表</param>
        public IllegalWordsForm(string title, string description, List<string> initialWords) : this(title, initialWords)
        {
            DescriptionText = description;
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                btnOK.Click += BtnOK_Click;
                btnCancel.Click += BtnCancel_Click;
                this.Load += IllegalWordsForm_Load;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置事件处理器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void IllegalWordsForm_Load(object? sender, EventArgs e)
        {
            try
            {
                // 设置焦点到文本框
                txtIllegalWords.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"窗体加载失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                SaveUIToWords();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存非法词失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// 加载非法词到界面
        /// </summary>
        private void LoadWordsToUI()
        {
            try
            {
                if (IllegalWords != null && IllegalWords.Count > 0)
                {
                    txtIllegalWords.Text = string.Join("\r\n", IllegalWords);
                }
                else
                {
                    txtIllegalWords.Text = string.Empty;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载非法词到界面失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存界面内容到非法词列表
        /// </summary>
        private void SaveUIToWords()
        {
            try
            {
                var text = txtIllegalWords.Text?.Trim() ?? string.Empty;
                if (string.IsNullOrEmpty(text))
                {
                    IllegalWords = new List<string>();
                }
                else
                {
                    IllegalWords = text.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries)
                        .Select(word => word.Trim())
                        .Where(word => !string.IsNullOrEmpty(word))
                        .Distinct()
                        .ToList();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存界面内容失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 获取当前非法词列表
        /// </summary>
        /// <returns>非法词列表</returns>
        public List<string> GetIllegalWords()
        {
            try
            {
                SaveUIToWords();
                return new List<string>(IllegalWords);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"获取非法词列表失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new List<string>();
            }
        }

        /// <summary>
        /// 设置非法词列表
        /// </summary>
        /// <param name="words">非法词列表</param>
        public void SetIllegalWords(List<string> words)
        {
            try
            {
                IllegalWords = words != null ? new List<string>(words) : new List<string>();
                LoadWordsToUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置非法词列表失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
